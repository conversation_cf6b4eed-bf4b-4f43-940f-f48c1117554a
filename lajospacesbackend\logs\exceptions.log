{"date":"Fri Jun 20 2025 13:09:33 GMT+0100 (West Africa Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module 'sharp'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts\nError: Cannot find module 'sharp'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1144:15)\n    at Function.Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js:811:30)\n    at Function.Module._load (node:internal/modules/cjs/loader:985:27)\n    at Module.require (node:internal/modules/cjs/loader:1235:19)\n    at require (node:internal/modules/helpers:176:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts:1:1)\n    at Module._compile (node:internal/modules/cjs/loader:1376:14)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1621:12)","os":{"loadavg":[0,0,0],"uptime":1854.89},"process":{"argv":["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts"],"cwd":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":25312772,"external":28389822,"heapTotal":131141632,"heapUsed":88966448,"rss":181948416},"pid":14620,"uid":null,"version":"v20.11.1"},"stack":"Error: Cannot find module 'sharp'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1144:15)\n    at Function.Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js:811:30)\n    at Function.Module._load (node:internal/modules/cjs/loader:985:27)\n    at Module.require (node:internal/modules/cjs/loader:1235:19)\n    at require (node:internal/modules/helpers:176:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts:1:1)\n    at Module._compile (node:internal/modules/cjs/loader:1376:14)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1621:12)","timestamp":"2025-06-20 13:09:33:933","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1144,"method":"_resolveFilename","native":false},{"column":30,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js","function":"Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename]","line":811,"method":"installedValue [as _resolveFilename]","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":985,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1235,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":176,"method":null,"native":false},{"column":1,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts","function":null,"line":1,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1376,"method":"_compile","native":false},{"column":23,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1618,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1435,"method":".js","native":false},{"column":12,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false}]}
{"date":"Fri Jun 20 2025 13:13:43 GMT+0100 (West Africa Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module 'sharp'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts\nError: Cannot find module 'sharp'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1144:15)\n    at Function.Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js:811:30)\n    at Function.Module._load (node:internal/modules/cjs/loader:985:27)\n    at Module.require (node:internal/modules/cjs/loader:1235:19)\n    at require (node:internal/modules/helpers:176:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts:1:1)\n    at Module._compile (node:internal/modules/cjs/loader:1376:14)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1621:12)","os":{"loadavg":[0,0,0],"uptime":2104.64},"process":{"argv":["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts"],"cwd":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":25312716,"external":28389766,"heapTotal":132714496,"heapUsed":91253552,"rss":181649408},"pid":23892,"uid":null,"version":"v20.11.1"},"stack":"Error: Cannot find module 'sharp'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1144:15)\n    at Function.Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js:811:30)\n    at Function.Module._load (node:internal/modules/cjs/loader:985:27)\n    at Module.require (node:internal/modules/cjs/loader:1235:19)\n    at require (node:internal/modules/helpers:176:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts:1:1)\n    at Module._compile (node:internal/modules/cjs/loader:1376:14)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1621:12)","timestamp":"2025-06-20 13:13:43:1343","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1144,"method":"_resolveFilename","native":false},{"column":30,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js","function":"Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename]","line":811,"method":"installedValue [as _resolveFilename]","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":985,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1235,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":176,"method":null,"native":false},{"column":1,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts","function":null,"line":1,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1376,"method":"_compile","native":false},{"column":23,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1618,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1435,"method":".js","native":false},{"column":12,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false}]}
{"date":"Fri Jun 20 2025 13:49:26 GMT+0100 (West Africa Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module 'sharp'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts\nError: Cannot find module 'sharp'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1144:15)\n    at Function.Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js:811:30)\n    at Function.Module._load (node:internal/modules/cjs/loader:985:27)\n    at Module.require (node:internal/modules/cjs/loader:1235:19)\n    at require (node:internal/modules/helpers:176:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts:1:1)\n    at Module._compile (node:internal/modules/cjs/loader:1376:14)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1621:12)","os":{"loadavg":[0,0,0],"uptime":4247.968},"process":{"argv":["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts"],"cwd":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":25312732,"external":28389782,"heapTotal":130879488,"heapUsed":91028784,"rss":175702016},"pid":16884,"uid":null,"version":"v20.11.1"},"stack":"Error: Cannot find module 'sharp'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1144:15)\n    at Function.Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js:811:30)\n    at Function.Module._load (node:internal/modules/cjs/loader:985:27)\n    at Module.require (node:internal/modules/cjs/loader:1235:19)\n    at require (node:internal/modules/helpers:176:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts:1:1)\n    at Module._compile (node:internal/modules/cjs/loader:1376:14)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1621:12)","timestamp":"2025-06-20 13:49:26:4926","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1144,"method":"_resolveFilename","native":false},{"column":30,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js","function":"Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename]","line":811,"method":"installedValue [as _resolveFilename]","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":985,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1235,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":176,"method":null,"native":false},{"column":1,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts","function":null,"line":1,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1376,"method":"_compile","native":false},{"column":23,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1618,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1435,"method":".js","native":false},{"column":12,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false}]}
{"date":"Fri Jun 20 2025 13:57:30 GMT+0100 (West Africa Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module 'sharp'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts\nError: Cannot find module 'sharp'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1144:15)\n    at Function.Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js:811:30)\n    at Function.Module._load (node:internal/modules/cjs/loader:985:27)\n    at Module.require (node:internal/modules/cjs/loader:1235:19)\n    at require (node:internal/modules/helpers:176:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts:1:1)\n    at Module._compile (node:internal/modules/cjs/loader:1376:14)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1621:12)","os":{"loadavg":[0,0,0],"uptime":4731.796},"process":{"argv":["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts"],"cwd":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":25312844,"external":28389894,"heapTotal":132976640,"heapUsed":91568712,"rss":177586176},"pid":11816,"uid":null,"version":"v20.11.1"},"stack":"Error: Cannot find module 'sharp'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1144:15)\n    at Function.Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js:811:30)\n    at Function.Module._load (node:internal/modules/cjs/loader:985:27)\n    at Module.require (node:internal/modules/cjs/loader:1235:19)\n    at require (node:internal/modules/helpers:176:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts:1:1)\n    at Module._compile (node:internal/modules/cjs/loader:1376:14)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1621:12)","timestamp":"2025-06-20 13:57:30:5730","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1144,"method":"_resolveFilename","native":false},{"column":30,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js","function":"Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename]","line":811,"method":"installedValue [as _resolveFilename]","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":985,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1235,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":176,"method":null,"native":false},{"column":1,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts","function":null,"line":1,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1376,"method":"_compile","native":false},{"column":23,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1618,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1435,"method":".js","native":false},{"column":12,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false}]}
{"date":"Fri Jun 20 2025 13:58:23 GMT+0100 (West Africa Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module 'sharp'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts\nError: Cannot find module 'sharp'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1144:15)\n    at Function.Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js:811:30)\n    at Function.Module._load (node:internal/modules/cjs/loader:985:27)\n    at Module.require (node:internal/modules/cjs/loader:1235:19)\n    at require (node:internal/modules/helpers:176:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts:1:1)\n    at Module._compile (node:internal/modules/cjs/loader:1376:14)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1621:12)","os":{"loadavg":[0,0,0],"uptime":4784.75},"process":{"argv":["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts"],"cwd":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":25296156,"external":28373206,"heapTotal":132714496,"heapUsed":89740656,"rss":178294784},"pid":19984,"uid":null,"version":"v20.11.1"},"stack":"Error: Cannot find module 'sharp'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1144:15)\n    at Function.Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js:811:30)\n    at Function.Module._load (node:internal/modules/cjs/loader:985:27)\n    at Module.require (node:internal/modules/cjs/loader:1235:19)\n    at require (node:internal/modules/helpers:176:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts:1:1)\n    at Module._compile (node:internal/modules/cjs/loader:1376:14)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1621:12)","timestamp":"2025-06-20 13:58:23:5823","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1144,"method":"_resolveFilename","native":false},{"column":30,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js","function":"Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename]","line":811,"method":"installedValue [as _resolveFilename]","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":985,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1235,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":176,"method":null,"native":false},{"column":1,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts","function":null,"line":1,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1376,"method":"_compile","native":false},{"column":23,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1618,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1435,"method":".js","native":false},{"column":12,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false}]}
{"date":"Fri Jun 20 2025 14:00:13 GMT+0100 (West Africa Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module 'sharp'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts\nError: Cannot find module 'sharp'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1144:15)\n    at Function.Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js:811:30)\n    at Function.Module._load (node:internal/modules/cjs/loader:985:27)\n    at Module.require (node:internal/modules/cjs/loader:1235:19)\n    at require (node:internal/modules/helpers:176:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts:1:1)\n    at Module._compile (node:internal/modules/cjs/loader:1376:14)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1621:12)","os":{"loadavg":[0,0,0],"uptime":4894.281},"process":{"argv":["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts"],"cwd":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":25312628,"external":28389678,"heapTotal":131403776,"heapUsed":89036048,"rss":176160768},"pid":6460,"uid":null,"version":"v20.11.1"},"stack":"Error: Cannot find module 'sharp'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1144:15)\n    at Function.Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js:811:30)\n    at Function.Module._load (node:internal/modules/cjs/loader:985:27)\n    at Module.require (node:internal/modules/cjs/loader:1235:19)\n    at require (node:internal/modules/helpers:176:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts:1:1)\n    at Module._compile (node:internal/modules/cjs/loader:1376:14)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1621:12)","timestamp":"2025-06-20 14:00:13:013","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1144,"method":"_resolveFilename","native":false},{"column":30,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js","function":"Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename]","line":811,"method":"installedValue [as _resolveFilename]","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":985,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1235,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":176,"method":null,"native":false},{"column":1,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts","function":null,"line":1,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1376,"method":"_compile","native":false},{"column":23,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1618,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1435,"method":".js","native":false},{"column":12,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false}]}
{"date":"Fri Jun 20 2025 14:03:07 GMT+0100 (West Africa Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module 'sharp'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts\nError: Cannot find module 'sharp'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1144:15)\n    at Function.Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js:811:30)\n    at Function.Module._load (node:internal/modules/cjs/loader:985:27)\n    at Module.require (node:internal/modules/cjs/loader:1235:19)\n    at require (node:internal/modules/helpers:176:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts:1:1)\n    at Module._compile (node:internal/modules/cjs/loader:1376:14)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1621:12)","os":{"loadavg":[0,0,0],"uptime":5068.812},"process":{"argv":["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts"],"cwd":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":25312644,"external":28389694,"heapTotal":133763072,"heapUsed":91592176,"rss":179175424},"pid":17844,"uid":null,"version":"v20.11.1"},"stack":"Error: Cannot find module 'sharp'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1144:15)\n    at Function.Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js:811:30)\n    at Function.Module._load (node:internal/modules/cjs/loader:985:27)\n    at Module.require (node:internal/modules/cjs/loader:1235:19)\n    at require (node:internal/modules/helpers:176:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts:1:1)\n    at Module._compile (node:internal/modules/cjs/loader:1376:14)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1621:12)","timestamp":"2025-06-20 14:03:07:37","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1144,"method":"_resolveFilename","native":false},{"column":30,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js","function":"Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename]","line":811,"method":"installedValue [as _resolveFilename]","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":985,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1235,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":176,"method":null,"native":false},{"column":1,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts","function":null,"line":1,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1376,"method":"_compile","native":false},{"column":23,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1618,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1435,"method":".js","native":false},{"column":12,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false}]}
{"date":"Fri Jun 20 2025 14:05:55 GMT+0100 (West Africa Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module 'sharp'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts\nError: Cannot find module 'sharp'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1144:15)\n    at Function.Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js:811:30)\n    at Function.Module._load (node:internal/modules/cjs/loader:985:27)\n    at Module.require (node:internal/modules/cjs/loader:1235:19)\n    at require (node:internal/modules/helpers:176:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts:1:1)\n    at Module._compile (node:internal/modules/cjs/loader:1376:14)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1621:12)","os":{"loadavg":[0,0,0],"uptime":5236.828},"process":{"argv":["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts"],"cwd":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":25312788,"external":28389838,"heapTotal":131928064,"heapUsed":91502576,"rss":175992832},"pid":9680,"uid":null,"version":"v20.11.1"},"stack":"Error: Cannot find module 'sharp'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1144:15)\n    at Function.Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js:811:30)\n    at Function.Module._load (node:internal/modules/cjs/loader:985:27)\n    at Module.require (node:internal/modules/cjs/loader:1235:19)\n    at require (node:internal/modules/helpers:176:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts:1:1)\n    at Module._compile (node:internal/modules/cjs/loader:1376:14)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1621:12)","timestamp":"2025-06-20 14:05:55:555","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1144,"method":"_resolveFilename","native":false},{"column":30,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js","function":"Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename]","line":811,"method":"installedValue [as _resolveFilename]","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":985,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1235,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":176,"method":null,"native":false},{"column":1,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts","function":null,"line":1,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1376,"method":"_compile","native":false},{"column":23,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1618,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1435,"method":".js","native":false},{"column":12,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false}]}
{"date":"Fri Jun 20 2025 14:16:18 GMT+0100 (West Africa Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module 'sharp'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts\nError: Cannot find module 'sharp'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1144:15)\n    at Function.Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js:811:30)\n    at Function.Module._load (node:internal/modules/cjs/loader:985:27)\n    at Module.require (node:internal/modules/cjs/loader:1235:19)\n    at require (node:internal/modules/helpers:176:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts:1:1)\n    at Module._compile (node:internal/modules/cjs/loader:1376:14)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1621:12)","os":{"loadavg":[0,0,0],"uptime":5859.765},"process":{"argv":["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts"],"cwd":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":25312892,"external":28389942,"heapTotal":131665920,"heapUsed":91741904,"rss":176144384},"pid":25292,"uid":null,"version":"v20.11.1"},"stack":"Error: Cannot find module 'sharp'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1144:15)\n    at Function.Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js:811:30)\n    at Function.Module._load (node:internal/modules/cjs/loader:985:27)\n    at Module.require (node:internal/modules/cjs/loader:1235:19)\n    at require (node:internal/modules/helpers:176:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts:1:1)\n    at Module._compile (node:internal/modules/cjs/loader:1376:14)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1621:12)","timestamp":"2025-06-20 14:16:18:1618","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1144,"method":"_resolveFilename","native":false},{"column":30,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js","function":"Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename]","line":811,"method":"installedValue [as _resolveFilename]","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":985,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1235,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":176,"method":null,"native":false},{"column":1,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts","function":null,"line":1,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1376,"method":"_compile","native":false},{"column":23,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1618,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1435,"method":".js","native":false},{"column":12,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false}]}
{"date":"Fri Jun 20 2025 14:34:14 GMT+0100 (West Africa Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module 'sharp'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts\nError: Cannot find module 'sharp'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1144:15)\n    at Function.Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js:811:30)\n    at Function.Module._load (node:internal/modules/cjs/loader:985:27)\n    at Module.require (node:internal/modules/cjs/loader:1235:19)\n    at require (node:internal/modules/helpers:176:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts:1:1)\n    at Module._compile (node:internal/modules/cjs/loader:1376:14)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1621:12)","os":{"loadavg":[0,0,0],"uptime":6936.187},"process":{"argv":["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts"],"cwd":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":25312812,"external":28389862,"heapTotal":130093056,"heapUsed":91266704,"rss":178458624},"pid":6528,"uid":null,"version":"v20.11.1"},"stack":"Error: Cannot find module 'sharp'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1144:15)\n    at Function.Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js:811:30)\n    at Function.Module._load (node:internal/modules/cjs/loader:985:27)\n    at Module.require (node:internal/modules/cjs/loader:1235:19)\n    at require (node:internal/modules/helpers:176:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts:1:1)\n    at Module._compile (node:internal/modules/cjs/loader:1376:14)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1621:12)","timestamp":"2025-06-20 14:34:14:3414","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1144,"method":"_resolveFilename","native":false},{"column":30,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js","function":"Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename]","line":811,"method":"installedValue [as _resolveFilename]","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":985,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1235,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":176,"method":null,"native":false},{"column":1,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts","function":null,"line":1,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1376,"method":"_compile","native":false},{"column":23,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1618,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1435,"method":".js","native":false},{"column":12,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false}]}
{"date":"Fri Jun 20 2025 14:54:27 GMT+0100 (West Africa Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module 'sharp'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts\nError: Cannot find module 'sharp'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1144:15)\n    at Function.Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js:811:30)\n    at Function.Module._load (node:internal/modules/cjs/loader:985:27)\n    at Module.require (node:internal/modules/cjs/loader:1235:19)\n    at require (node:internal/modules/helpers:176:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts:1:1)\n    at Module._compile (node:internal/modules/cjs/loader:1376:14)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1621:12)","os":{"loadavg":[0,0,0],"uptime":8149.156},"process":{"argv":["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts"],"cwd":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":25312732,"external":28389782,"heapTotal":134287360,"heapUsed":91427152,"rss":182067200},"pid":19060,"uid":null,"version":"v20.11.1"},"stack":"Error: Cannot find module 'sharp'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1144:15)\n    at Function.Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js:811:30)\n    at Function.Module._load (node:internal/modules/cjs/loader:985:27)\n    at Module.require (node:internal/modules/cjs/loader:1235:19)\n    at require (node:internal/modules/helpers:176:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts:1:1)\n    at Module._compile (node:internal/modules/cjs/loader:1376:14)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1621:12)","timestamp":"2025-06-20 14:54:27:5427","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1144,"method":"_resolveFilename","native":false},{"column":30,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js","function":"Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename]","line":811,"method":"installedValue [as _resolveFilename]","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":985,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1235,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":176,"method":null,"native":false},{"column":1,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts","function":null,"line":1,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1376,"method":"_compile","native":false},{"column":23,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1618,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1435,"method":".js","native":false},{"column":12,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false}]}
{"date":"Fri Jun 20 2025 14:58:47 GMT+0100 (West Africa Standard Time)","error":{"address":"::","code":"EADDRINUSE","errno":-4091,"port":5000,"syscall":"listen"},"exception":true,"level":"error","message":"uncaughtException: listen EADDRINUSE: address already in use :::5000\nError: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1872:16)\n    at listenInCluster (node:net:1920:12)\n    at Server.listen (node:net:2008:7)\n    at startServer (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts:235:31)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","os":{"loadavg":[0,0,0],"uptime":8408.796},"process":{"argv":["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts"],"cwd":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":25312692,"external":28410998,"heapTotal":141484032,"heapUsed":86945952,"rss":192114688},"pid":23824,"uid":null,"version":"v20.11.1"},"stack":"Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1872:16)\n    at listenInCluster (node:net:1920:12)\n    at Server.listen (node:net:2008:7)\n    at startServer (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts:235:31)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-20 14:58:47:5847","trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1872,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1920,"method":null,"native":false},{"column":7,"file":"node:net","function":"Server.listen","line":2008,"method":"listen","native":false},{"column":31,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts","function":"startServer","line":235,"method":null,"native":false},{"column":5,"file":"node:internal/process/task_queues","function":"processTicksAndRejections","line":95,"method":null,"native":false}]}
{"date":"Fri Jun 20 2025 15:40:12 GMT+0100 (West Africa Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module 'sharp'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts\nError: Cannot find module 'sharp'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1144:15)\n    at Function.Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js:811:30)\n    at Function.Module._load (node:internal/modules/cjs/loader:985:27)\n    at Module.require (node:internal/modules/cjs/loader:1235:19)\n    at require (node:internal/modules/helpers:176:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts:1:1)\n    at Module._compile (node:internal/modules/cjs/loader:1376:14)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1621:12)","os":{"loadavg":[0,0,0],"uptime":10893.234},"process":{"argv":["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts"],"cwd":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":25312820,"external":28389870,"heapTotal":132714496,"heapUsed":91597616,"rss":183685120},"pid":22348,"uid":null,"version":"v20.11.1"},"stack":"Error: Cannot find module 'sharp'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1144:15)\n    at Function.Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js:811:30)\n    at Function.Module._load (node:internal/modules/cjs/loader:985:27)\n    at Module.require (node:internal/modules/cjs/loader:1235:19)\n    at require (node:internal/modules/helpers:176:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts:1:1)\n    at Module._compile (node:internal/modules/cjs/loader:1376:14)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1621:12)","timestamp":"2025-06-20 15:40:12:4012","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1144,"method":"_resolveFilename","native":false},{"column":30,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js","function":"Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename]","line":811,"method":"installedValue [as _resolveFilename]","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":985,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1235,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":176,"method":null,"native":false},{"column":1,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts","function":null,"line":1,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1376,"method":"_compile","native":false},{"column":23,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1618,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1435,"method":".js","native":false},{"column":12,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false}]}
{"date":"Fri Jun 20 2025 15:43:41 GMT+0100 (West Africa Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module 'sharp'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts\nError: Cannot find module 'sharp'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1144:15)\n    at Function.Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js:811:30)\n    at Function.Module._load (node:internal/modules/cjs/loader:985:27)\n    at Module.require (node:internal/modules/cjs/loader:1235:19)\n    at require (node:internal/modules/helpers:176:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts:1:1)\n    at Module._compile (node:internal/modules/cjs/loader:1376:14)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1621:12)","os":{"loadavg":[0,0,0],"uptime":11103.015},"process":{"argv":["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts"],"cwd":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":25312884,"external":28389934,"heapTotal":132714496,"heapUsed":90983448,"rss":183332864},"pid":20696,"uid":null,"version":"v20.11.1"},"stack":"Error: Cannot find module 'sharp'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1144:15)\n    at Function.Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js:811:30)\n    at Function.Module._load (node:internal/modules/cjs/loader:985:27)\n    at Module.require (node:internal/modules/cjs/loader:1235:19)\n    at require (node:internal/modules/helpers:176:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts:1:1)\n    at Module._compile (node:internal/modules/cjs/loader:1376:14)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1621:12)","timestamp":"2025-06-20 15:43:41:4341","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1144,"method":"_resolveFilename","native":false},{"column":30,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js","function":"Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename]","line":811,"method":"installedValue [as _resolveFilename]","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":985,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1235,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":176,"method":null,"native":false},{"column":1,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts","function":null,"line":1,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1376,"method":"_compile","native":false},{"column":23,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1618,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1435,"method":".js","native":false},{"column":12,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false}]}
{"date":"Fri Jun 20 2025 16:07:44 GMT+0100 (West Africa Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module 'sharp'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts\nError: Cannot find module 'sharp'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1144:15)\n    at Function.Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js:811:30)\n    at Function.Module._load (node:internal/modules/cjs/loader:985:27)\n    at Module.require (node:internal/modules/cjs/loader:1235:19)\n    at require (node:internal/modules/helpers:176:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts:1:1)\n    at Module._compile (node:internal/modules/cjs/loader:1376:14)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1621:12)","os":{"loadavg":[0,0,0],"uptime":12545.921},"process":{"argv":["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts"],"cwd":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":25312828,"external":28389878,"heapTotal":131928064,"heapUsed":91357704,"rss":177504256},"pid":21412,"uid":null,"version":"v20.11.1"},"stack":"Error: Cannot find module 'sharp'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1144:15)\n    at Function.Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js:811:30)\n    at Function.Module._load (node:internal/modules/cjs/loader:985:27)\n    at Module.require (node:internal/modules/cjs/loader:1235:19)\n    at require (node:internal/modules/helpers:176:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts:1:1)\n    at Module._compile (node:internal/modules/cjs/loader:1376:14)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1621:12)","timestamp":"2025-06-20 16:07:44:744","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1144,"method":"_resolveFilename","native":false},{"column":30,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js","function":"Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename]","line":811,"method":"installedValue [as _resolveFilename]","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":985,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1235,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":176,"method":null,"native":false},{"column":1,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts","function":null,"line":1,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1376,"method":"_compile","native":false},{"column":23,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1618,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1435,"method":".js","native":false},{"column":12,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false}]}
{"date":"Fri Jun 20 2025 16:08:51 GMT+0100 (West Africa Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module 'sharp'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts\nError: Cannot find module 'sharp'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1144:15)\n    at Function.Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js:811:30)\n    at Function.Module._load (node:internal/modules/cjs/loader:985:27)\n    at Module.require (node:internal/modules/cjs/loader:1235:19)\n    at require (node:internal/modules/helpers:176:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts:1:1)\n    at Module._compile (node:internal/modules/cjs/loader:1376:14)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1621:12)","os":{"loadavg":[0,0,0],"uptime":12612.5},"process":{"argv":["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts"],"cwd":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":25301770,"external":28378820,"heapTotal":131665920,"heapUsed":67537096,"rss":177389568},"pid":19808,"uid":null,"version":"v20.11.1"},"stack":"Error: Cannot find module 'sharp'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1144:15)\n    at Function.Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js:811:30)\n    at Function.Module._load (node:internal/modules/cjs/loader:985:27)\n    at Module.require (node:internal/modules/cjs/loader:1235:19)\n    at require (node:internal/modules/helpers:176:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts:1:1)\n    at Module._compile (node:internal/modules/cjs/loader:1376:14)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1621:12)","timestamp":"2025-06-20 16:08:51:851","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1144,"method":"_resolveFilename","native":false},{"column":30,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js","function":"Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename]","line":811,"method":"installedValue [as _resolveFilename]","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":985,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1235,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":176,"method":null,"native":false},{"column":1,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts","function":null,"line":1,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1376,"method":"_compile","native":false},{"column":23,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1618,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1435,"method":".js","native":false},{"column":12,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false}]}
{"date":"Fri Jun 20 2025 16:13:15 GMT+0100 (West Africa Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module 'sharp'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts\nError: Cannot find module 'sharp'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1144:15)\n    at Function.Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js:811:30)\n    at Function.Module._load (node:internal/modules/cjs/loader:985:27)\n    at Module.require (node:internal/modules/cjs/loader:1235:19)\n    at require (node:internal/modules/helpers:176:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts:1:1)\n    at Module._compile (node:internal/modules/cjs/loader:1376:14)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1621:12)","os":{"loadavg":[0,0,0],"uptime":12876.328},"process":{"argv":["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts"],"cwd":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":25301738,"external":28378788,"heapTotal":130617344,"heapUsed":68869088,"rss":176001024},"pid":20616,"uid":null,"version":"v20.11.1"},"stack":"Error: Cannot find module 'sharp'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1144:15)\n    at Function.Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js:811:30)\n    at Function.Module._load (node:internal/modules/cjs/loader:985:27)\n    at Module.require (node:internal/modules/cjs/loader:1235:19)\n    at require (node:internal/modules/helpers:176:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts:1:1)\n    at Module._compile (node:internal/modules/cjs/loader:1376:14)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1621:12)","timestamp":"2025-06-20 16:13:15:1315","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1144,"method":"_resolveFilename","native":false},{"column":30,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js","function":"Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename]","line":811,"method":"installedValue [as _resolveFilename]","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":985,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1235,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":176,"method":null,"native":false},{"column":1,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts","function":null,"line":1,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1376,"method":"_compile","native":false},{"column":23,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1618,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1435,"method":".js","native":false},{"column":12,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false}]}
{"date":"Fri Jun 20 2025 16:17:23 GMT+0100 (West Africa Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module 'sharp'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts\nError: Cannot find module 'sharp'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1144:15)\n    at Function.Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js:811:30)\n    at Function.Module._load (node:internal/modules/cjs/loader:985:27)\n    at Module.require (node:internal/modules/cjs/loader:1235:19)\n    at require (node:internal/modules/helpers:176:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts:1:1)\n    at Module._compile (node:internal/modules/cjs/loader:1376:14)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1621:12)","os":{"loadavg":[0,0,0],"uptime":13124.265},"process":{"argv":["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts"],"cwd":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":25301674,"external":28378724,"heapTotal":131665920,"heapUsed":67896144,"rss":177119232},"pid":13772,"uid":null,"version":"v20.11.1"},"stack":"Error: Cannot find module 'sharp'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1144:15)\n    at Function.Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js:811:30)\n    at Function.Module._load (node:internal/modules/cjs/loader:985:27)\n    at Module.require (node:internal/modules/cjs/loader:1235:19)\n    at require (node:internal/modules/helpers:176:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts:1:1)\n    at Module._compile (node:internal/modules/cjs/loader:1376:14)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1621:12)","timestamp":"2025-06-20 16:17:23:1723","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1144,"method":"_resolveFilename","native":false},{"column":30,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js","function":"Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename]","line":811,"method":"installedValue [as _resolveFilename]","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":985,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1235,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":176,"method":null,"native":false},{"column":1,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts","function":null,"line":1,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1376,"method":"_compile","native":false},{"column":23,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1618,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1435,"method":".js","native":false},{"column":12,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false}]}
{"date":"Fri Jun 20 2025 16:21:40 GMT+0100 (West Africa Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module 'sharp'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts\nError: Cannot find module 'sharp'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1144:15)\n    at Function.Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js:811:30)\n    at Function.Module._load (node:internal/modules/cjs/loader:985:27)\n    at Module.require (node:internal/modules/cjs/loader:1235:19)\n    at require (node:internal/modules/helpers:176:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts:1:1)\n    at Module._compile (node:internal/modules/cjs/loader:1376:14)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1621:12)","os":{"loadavg":[0,0,0],"uptime":13381.531},"process":{"argv":["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts"],"cwd":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":25273075,"external":28350125,"heapTotal":132452352,"heapUsed":74347032,"rss":180072448},"pid":20528,"uid":null,"version":"v20.11.1"},"stack":"Error: Cannot find module 'sharp'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1144:15)\n    at Function.Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js:811:30)\n    at Function.Module._load (node:internal/modules/cjs/loader:985:27)\n    at Module.require (node:internal/modules/cjs/loader:1235:19)\n    at require (node:internal/modules/helpers:176:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts:1:1)\n    at Module._compile (node:internal/modules/cjs/loader:1376:14)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1621:12)","timestamp":"2025-06-20 16:21:40:2140","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1144,"method":"_resolveFilename","native":false},{"column":30,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js","function":"Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename]","line":811,"method":"installedValue [as _resolveFilename]","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":985,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1235,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":176,"method":null,"native":false},{"column":1,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts","function":null,"line":1,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1376,"method":"_compile","native":false},{"column":23,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1618,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1435,"method":".js","native":false},{"column":12,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false}]}
{"date":"Fri Jun 20 2025 16:34:59 GMT+0100 (West Africa Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module 'sharp'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts\nError: Cannot find module 'sharp'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1144:15)\n    at Function.Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js:811:30)\n    at Function.Module._load (node:internal/modules/cjs/loader:985:27)\n    at Module.require (node:internal/modules/cjs/loader:1235:19)\n    at require (node:internal/modules/helpers:176:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts:1:1)\n    at Module._compile (node:internal/modules/cjs/loader:1376:14)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1621:12)","os":{"loadavg":[0,0,0],"uptime":14180.5},"process":{"argv":["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts"],"cwd":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":25312700,"external":28389750,"heapTotal":132976640,"heapUsed":91595592,"rss":178421760},"pid":22960,"uid":null,"version":"v20.11.1"},"stack":"Error: Cannot find module 'sharp'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1144:15)\n    at Function.Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js:811:30)\n    at Function.Module._load (node:internal/modules/cjs/loader:985:27)\n    at Module.require (node:internal/modules/cjs/loader:1235:19)\n    at require (node:internal/modules/helpers:176:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts:1:1)\n    at Module._compile (node:internal/modules/cjs/loader:1376:14)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1621:12)","timestamp":"2025-06-20 16:34:59:3459","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1144,"method":"_resolveFilename","native":false},{"column":30,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js","function":"Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename]","line":811,"method":"installedValue [as _resolveFilename]","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":985,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1235,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":176,"method":null,"native":false},{"column":1,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts","function":null,"line":1,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1376,"method":"_compile","native":false},{"column":23,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1618,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1435,"method":".js","native":false},{"column":12,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false}]}
{"date":"Fri Jun 20 2025 16:37:51 GMT+0100 (West Africa Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module 'sharp'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts\nError: Cannot find module 'sharp'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1144:15)\n    at Function.Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js:811:30)\n    at Function.Module._load (node:internal/modules/cjs/loader:985:27)\n    at Module.require (node:internal/modules/cjs/loader:1235:19)\n    at require (node:internal/modules/helpers:176:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts:1:1)\n    at Module._compile (node:internal/modules/cjs/loader:1376:14)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1621:12)","os":{"loadavg":[0,0,0],"uptime":14352.875},"process":{"argv":["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts"],"cwd":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":25312740,"external":28389790,"heapTotal":131928064,"heapUsed":90897472,"rss":180756480},"pid":14392,"uid":null,"version":"v20.11.1"},"stack":"Error: Cannot find module 'sharp'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1144:15)\n    at Function.Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js:811:30)\n    at Function.Module._load (node:internal/modules/cjs/loader:985:27)\n    at Module.require (node:internal/modules/cjs/loader:1235:19)\n    at require (node:internal/modules/helpers:176:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts:1:1)\n    at Module._compile (node:internal/modules/cjs/loader:1376:14)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1621:12)","timestamp":"2025-06-20 16:37:51:3751","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1144,"method":"_resolveFilename","native":false},{"column":30,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js","function":"Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename]","line":811,"method":"installedValue [as _resolveFilename]","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":985,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1235,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":176,"method":null,"native":false},{"column":1,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts","function":null,"line":1,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1376,"method":"_compile","native":false},{"column":23,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1618,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1435,"method":".js","native":false},{"column":12,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false}]}
{"date":"Fri Jun 20 2025 16:44:05 GMT+0100 (West Africa Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module 'sharp'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts\nError: Cannot find module 'sharp'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1144:15)\n    at Function.Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js:811:30)\n    at Function.Module._load (node:internal/modules/cjs/loader:985:27)\n    at Module.require (node:internal/modules/cjs/loader:1235:19)\n    at require (node:internal/modules/helpers:176:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts:1:1)\n    at Module._compile (node:internal/modules/cjs/loader:1376:14)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1621:12)","os":{"loadavg":[0,0,0],"uptime":14726.781},"process":{"argv":["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts"],"cwd":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":25318262,"external":28396203,"heapTotal":135806976,"heapUsed":116968664,"rss":181530624},"pid":25584,"uid":null,"version":"v20.11.1"},"stack":"Error: Cannot find module 'sharp'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1144:15)\n    at Function.Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js:811:30)\n    at Function.Module._load (node:internal/modules/cjs/loader:985:27)\n    at Module.require (node:internal/modules/cjs/loader:1235:19)\n    at require (node:internal/modules/helpers:176:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts:1:1)\n    at Module._compile (node:internal/modules/cjs/loader:1376:14)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1621:12)","timestamp":"2025-06-20 16:44:05:445","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1144,"method":"_resolveFilename","native":false},{"column":30,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js","function":"Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename]","line":811,"method":"installedValue [as _resolveFilename]","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":985,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1235,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":176,"method":null,"native":false},{"column":1,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts","function":null,"line":1,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1376,"method":"_compile","native":false},{"column":23,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1618,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1435,"method":".js","native":false},{"column":12,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false}]}
{"date":"Fri Jun 20 2025 16:49:49 GMT+0100 (West Africa Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module 'sharp'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts\nError: Cannot find module 'sharp'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1144:15)\n    at Function.Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js:811:30)\n    at Function.Module._load (node:internal/modules/cjs/loader:985:27)\n    at Module.require (node:internal/modules/cjs/loader:1235:19)\n    at require (node:internal/modules/helpers:176:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts:1:1)\n    at Module._compile (node:internal/modules/cjs/loader:1376:14)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1621:12)","os":{"loadavg":[0,0,0],"uptime":15070.953},"process":{"argv":["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts"],"cwd":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":25217371,"external":28295312,"heapTotal":140333056,"heapUsed":108611464,"rss":185122816},"pid":19504,"uid":null,"version":"v20.11.1"},"stack":"Error: Cannot find module 'sharp'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1144:15)\n    at Function.Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js:811:30)\n    at Function.Module._load (node:internal/modules/cjs/loader:985:27)\n    at Module.require (node:internal/modules/cjs/loader:1235:19)\n    at require (node:internal/modules/helpers:176:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts:1:1)\n    at Module._compile (node:internal/modules/cjs/loader:1376:14)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1621:12)","timestamp":"2025-06-20 16:49:49:4949","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1144,"method":"_resolveFilename","native":false},{"column":30,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js","function":"Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename]","line":811,"method":"installedValue [as _resolveFilename]","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":985,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1235,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":176,"method":null,"native":false},{"column":1,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts","function":null,"line":1,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1376,"method":"_compile","native":false},{"column":23,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1618,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1435,"method":".js","native":false},{"column":12,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false}]}
{"date":"Fri Jun 20 2025 16:55:18 GMT+0100 (West Africa Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module 'sharp'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts\nError: Cannot find module 'sharp'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1144:15)\n    at Function.Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js:811:30)\n    at Function.Module._load (node:internal/modules/cjs/loader:985:27)\n    at Module.require (node:internal/modules/cjs/loader:1235:19)\n    at require (node:internal/modules/helpers:176:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts:1:1)\n    at Module._compile (node:internal/modules/cjs/loader:1376:14)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1621:12)","os":{"loadavg":[0,0,0],"uptime":15400.031},"process":{"argv":["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts"],"cwd":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":25256267,"external":28334208,"heapTotal":140070912,"heapUsed":108842616,"rss":186646528},"pid":10620,"uid":null,"version":"v20.11.1"},"stack":"Error: Cannot find module 'sharp'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1144:15)\n    at Function.Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js:811:30)\n    at Function.Module._load (node:internal/modules/cjs/loader:985:27)\n    at Module.require (node:internal/modules/cjs/loader:1235:19)\n    at require (node:internal/modules/helpers:176:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts:1:1)\n    at Module._compile (node:internal/modules/cjs/loader:1376:14)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1621:12)","timestamp":"2025-06-20 16:55:18:5518","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1144,"method":"_resolveFilename","native":false},{"column":30,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js","function":"Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename]","line":811,"method":"installedValue [as _resolveFilename]","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":985,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1235,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":176,"method":null,"native":false},{"column":1,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts","function":null,"line":1,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1376,"method":"_compile","native":false},{"column":23,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1618,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1435,"method":".js","native":false},{"column":12,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false}]}
{"date":"Fri Jun 20 2025 16:57:39 GMT+0100 (West Africa Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module 'sharp'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts\nError: Cannot find module 'sharp'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1144:15)\n    at Function.Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js:811:30)\n    at Function.Module._load (node:internal/modules/cjs/loader:985:27)\n    at Module.require (node:internal/modules/cjs/loader:1235:19)\n    at require (node:internal/modules/helpers:176:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts:1:1)\n    at Module._compile (node:internal/modules/cjs/loader:1376:14)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1621:12)","os":{"loadavg":[0,0,0],"uptime":15540.328},"process":{"argv":["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts"],"cwd":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":25217315,"external":28295256,"heapTotal":140595200,"heapUsed":108667648,"rss":185225216},"pid":6192,"uid":null,"version":"v20.11.1"},"stack":"Error: Cannot find module 'sharp'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1144:15)\n    at Function.Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js:811:30)\n    at Function.Module._load (node:internal/modules/cjs/loader:985:27)\n    at Module.require (node:internal/modules/cjs/loader:1235:19)\n    at require (node:internal/modules/helpers:176:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts:1:1)\n    at Module._compile (node:internal/modules/cjs/loader:1376:14)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1621:12)","timestamp":"2025-06-20 16:57:39:5739","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1144,"method":"_resolveFilename","native":false},{"column":30,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js","function":"Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename]","line":811,"method":"installedValue [as _resolveFilename]","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":985,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1235,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":176,"method":null,"native":false},{"column":1,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts","function":null,"line":1,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1376,"method":"_compile","native":false},{"column":23,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1618,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1435,"method":".js","native":false},{"column":12,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false}]}
{"date":"Fri Jun 20 2025 16:58:45 GMT+0100 (West Africa Standard Time)","error":{"code":"MODULE_NOT_FOUND","requireStack":["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module 'sharp'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts\nError: Cannot find module 'sharp'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1144:15)\n    at Function.Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js:811:30)\n    at Function.Module._load (node:internal/modules/cjs/loader:985:27)\n    at Module.require (node:internal/modules/cjs/loader:1235:19)\n    at require (node:internal/modules/helpers:176:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts:1:1)\n    at Module._compile (node:internal/modules/cjs/loader:1376:14)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1621:12)","os":{"loadavg":[0,0,0],"uptime":15606.421},"process":{"argv":["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts"],"cwd":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":25318294,"external":28396235,"heapTotal":135090176,"heapUsed":114814416,"rss":180023296},"pid":5920,"uid":null,"version":"v20.11.1"},"stack":"Error: Cannot find module 'sharp'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1144:15)\n    at Function.Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js:811:30)\n    at Function.Module._load (node:internal/modules/cjs/loader:985:27)\n    at Module.require (node:internal/modules/cjs/loader:1235:19)\n    at require (node:internal/modules/helpers:176:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts:1:1)\n    at Module._compile (node:internal/modules/cjs/loader:1376:14)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1621:12)","timestamp":"2025-06-20 16:58:45:5845","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1144,"method":"_resolveFilename","native":false},{"column":30,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js","function":"Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename]","line":811,"method":"installedValue [as _resolveFilename]","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":985,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1235,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":176,"method":null,"native":false},{"column":1,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts","function":null,"line":1,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1376,"method":"_compile","native":false},{"column":23,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1618,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1435,"method":".js","native":false},{"column":12,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false}]}
{"date":"Fri Jun 20 2025 18:08:07 GMT+0100 (West Africa Standard Time)","error":{"address":"::","code":"EADDRINUSE","errno":-4091,"port":5000,"syscall":"listen"},"exception":true,"level":"error","message":"uncaughtException: listen EADDRINUSE: address already in use :::5000\nError: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1872:16)\n    at listenInCluster (node:net:1920:12)\n    at Server.listen (node:net:2008:7)\n    at startServer (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts:303:31)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","os":{"loadavg":[0,0,0],"uptime":19768.25},"process":{"argv":["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts"],"cwd":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":25336961,"external":28434376,"heapTotal":184545280,"heapUsed":151619696,"rss":239091712},"pid":24864,"uid":null,"version":"v20.11.1"},"stack":"Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1872:16)\n    at listenInCluster (node:net:1920:12)\n    at Server.listen (node:net:2008:7)\n    at startServer (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts:303:31)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-20 18:08:07:87","trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1872,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1920,"method":null,"native":false},{"column":7,"file":"node:net","function":"Server.listen","line":2008,"method":"listen","native":false},{"column":31,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts","function":"startServer","line":303,"method":null,"native":false},{"column":5,"file":"node:internal/process/task_queues","function":"processTicksAndRejections","line":95,"method":null,"native":false}]}
{"date":"Fri Jun 20 2025 18:34:02 GMT+0100 (West Africa Standard Time)","error":{"address":"::","code":"EADDRINUSE","errno":-4091,"port":5000,"syscall":"listen"},"exception":true,"level":"error","message":"uncaughtException: listen EADDRINUSE: address already in use :::5000\nError: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1872:16)\n    at listenInCluster (node:net:1920:12)\n    at Server.listen (node:net:2008:7)\n    at startServer (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts:303:31)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","os":{"loadavg":[0,0,0],"uptime":21323.718},"process":{"argv":["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts"],"cwd":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":25407929,"external":28504133,"heapTotal":155103232,"heapUsed":130300496,"rss":212447232},"pid":26388,"uid":null,"version":"v20.11.1"},"stack":"Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1872:16)\n    at listenInCluster (node:net:1920:12)\n    at Server.listen (node:net:2008:7)\n    at startServer (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts:303:31)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-20 18:34:02:342","trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1872,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1920,"method":null,"native":false},{"column":7,"file":"node:net","function":"Server.listen","line":2008,"method":"listen","native":false},{"column":31,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts","function":"startServer","line":303,"method":null,"native":false},{"column":5,"file":"node:internal/process/task_queues","function":"processTicksAndRejections","line":95,"method":null,"native":false}]}
{"date":"Fri Jun 20 2025 18:36:57 GMT+0100 (West Africa Standard Time)","error":{"address":"::","code":"EADDRINUSE","errno":-4091,"port":5000,"syscall":"listen"},"exception":true,"level":"error","message":"uncaughtException: listen EADDRINUSE: address already in use :::5000\nError: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1872:16)\n    at listenInCluster (node:net:1920:12)\n    at Server.listen (node:net:2008:7)\n    at startServer (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts:303:31)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","os":{"loadavg":[0,0,0],"uptime":21498.218},"process":{"argv":["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts"],"cwd":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":25337065,"external":28434480,"heapTotal":184283136,"heapUsed":151631992,"rss":239771648},"pid":1668,"uid":null,"version":"v20.11.1"},"stack":"Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1872:16)\n    at listenInCluster (node:net:1920:12)\n    at Server.listen (node:net:2008:7)\n    at startServer (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts:303:31)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-20 18:36:57:3657","trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1872,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1920,"method":null,"native":false},{"column":7,"file":"node:net","function":"Server.listen","line":2008,"method":"listen","native":false},{"column":31,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts","function":"startServer","line":303,"method":null,"native":false},{"column":5,"file":"node:internal/process/task_queues","function":"processTicksAndRejections","line":95,"method":null,"native":false}]}
{"date":"Fri Jun 20 2025 18:39:52 GMT+0100 (West Africa Standard Time)","error":{"address":"::","code":"EADDRINUSE","errno":-4091,"port":5000,"syscall":"listen"},"exception":true,"level":"error","message":"uncaughtException: listen EADDRINUSE: address already in use :::5000\nError: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1872:16)\n    at listenInCluster (node:net:1920:12)\n    at Server.listen (node:net:2008:7)\n    at startServer (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts:303:31)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","os":{"loadavg":[0,0,0],"uptime":21673.265},"process":{"argv":["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts"],"cwd":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":25407728,"external":28503932,"heapTotal":151924736,"heapUsed":129627208,"rss":208003072},"pid":25808,"uid":null,"version":"v20.11.1"},"stack":"Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1872:16)\n    at listenInCluster (node:net:1920:12)\n    at Server.listen (node:net:2008:7)\n    at startServer (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts:303:31)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-20 18:39:52:3952","trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1872,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1920,"method":null,"native":false},{"column":7,"file":"node:net","function":"Server.listen","line":2008,"method":"listen","native":false},{"column":31,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts","function":"startServer","line":303,"method":null,"native":false},{"column":5,"file":"node:internal/process/task_queues","function":"processTicksAndRejections","line":95,"method":null,"native":false}]}
{"date":"Fri Jun 20 2025 18:41:45 GMT+0100 (West Africa Standard Time)","error":{"address":"::","code":"EADDRINUSE","errno":-4091,"port":5000,"syscall":"listen"},"exception":true,"level":"error","message":"uncaughtException: listen EADDRINUSE: address already in use :::5000\nError: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1872:16)\n    at listenInCluster (node:net:1920:12)\n    at Server.listen (node:net:2008:7)\n    at startServer (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts:303:31)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","os":{"loadavg":[0,0,0],"uptime":21786.531},"process":{"argv":["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts"],"cwd":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":25412168,"external":28508372,"heapTotal":152449024,"heapUsed":129476312,"rss":207847424},"pid":17648,"uid":null,"version":"v20.11.1"},"stack":"Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1872:16)\n    at listenInCluster (node:net:1920:12)\n    at Server.listen (node:net:2008:7)\n    at startServer (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts:303:31)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-20 18:41:45:4145","trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1872,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1920,"method":null,"native":false},{"column":7,"file":"node:net","function":"Server.listen","line":2008,"method":"listen","native":false},{"column":31,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts","function":"startServer","line":303,"method":null,"native":false},{"column":5,"file":"node:internal/process/task_queues","function":"processTicksAndRejections","line":95,"method":null,"native":false}]}
{"date":"Fri Jun 20 2025 18:43:22 GMT+0100 (West Africa Standard Time)","error":{"address":"::","code":"EADDRINUSE","errno":-4091,"port":5000,"syscall":"listen"},"exception":true,"level":"error","message":"uncaughtException: listen EADDRINUSE: address already in use :::5000\nError: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1872:16)\n    at listenInCluster (node:net:1920:12)\n    at Server.listen (node:net:2008:7)\n    at startServer (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts:303:31)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","os":{"loadavg":[0,0,0],"uptime":21884.203},"process":{"argv":["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts"],"cwd":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":25411824,"external":28508028,"heapTotal":148230144,"heapUsed":120452720,"rss":202493952},"pid":27164,"uid":null,"version":"v20.11.1"},"stack":"Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1872:16)\n    at listenInCluster (node:net:1920:12)\n    at Server.listen (node:net:2008:7)\n    at startServer (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts:303:31)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-20 18:43:22:4322","trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1872,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1920,"method":null,"native":false},{"column":7,"file":"node:net","function":"Server.listen","line":2008,"method":"listen","native":false},{"column":31,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts","function":"startServer","line":303,"method":null,"native":false},{"column":5,"file":"node:internal/process/task_queues","function":"processTicksAndRejections","line":95,"method":null,"native":false}]}
{"date":"Fri Jun 20 2025 18:44:48 GMT+0100 (West Africa Standard Time)","error":{"address":"::","code":"EADDRINUSE","errno":-4091,"port":5000,"syscall":"listen"},"exception":true,"level":"error","message":"uncaughtException: listen EADDRINUSE: address already in use :::5000\nError: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1872:16)\n    at listenInCluster (node:net:1920:12)\n    at Server.listen (node:net:2008:7)\n    at startServer (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts:303:31)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","os":{"loadavg":[0,0,0],"uptime":21969.781},"process":{"argv":["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts"],"cwd":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":25361792,"external":28459207,"heapTotal":158892032,"heapUsed":130361008,"rss":214790144},"pid":21916,"uid":null,"version":"v20.11.1"},"stack":"Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1872:16)\n    at listenInCluster (node:net:1920:12)\n    at Server.listen (node:net:2008:7)\n    at startServer (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts:303:31)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-20 18:44:48:4448","trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1872,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1920,"method":null,"native":false},{"column":7,"file":"node:net","function":"Server.listen","line":2008,"method":"listen","native":false},{"column":31,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts","function":"startServer","line":303,"method":null,"native":false},{"column":5,"file":"node:internal/process/task_queues","function":"processTicksAndRejections","line":95,"method":null,"native":false}]}
{"date":"Fri Jun 20 2025 18:46:21 GMT+0100 (West Africa Standard Time)","error":{"address":"::","code":"EADDRINUSE","errno":-4091,"port":5000,"syscall":"listen"},"exception":true,"level":"error","message":"uncaughtException: listen EADDRINUSE: address already in use :::5000\nError: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1872:16)\n    at listenInCluster (node:net:1920:12)\n    at Server.listen (node:net:2008:7)\n    at startServer (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts:303:31)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","os":{"loadavg":[0,0,0],"uptime":22063.062},"process":{"argv":["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts"],"cwd":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":25352903,"external":28450318,"heapTotal":146808832,"heapUsed":121365312,"rss":200810496},"pid":25792,"uid":null,"version":"v20.11.1"},"stack":"Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1872:16)\n    at listenInCluster (node:net:1920:12)\n    at Server.listen (node:net:2008:7)\n    at startServer (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts:303:31)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-20 18:46:21:4621","trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1872,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1920,"method":null,"native":false},{"column":7,"file":"node:net","function":"Server.listen","line":2008,"method":"listen","native":false},{"column":31,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts","function":"startServer","line":303,"method":null,"native":false},{"column":5,"file":"node:internal/process/task_queues","function":"processTicksAndRejections","line":95,"method":null,"native":false}]}
{"date":"Fri Jun 20 2025 18:59:21 GMT+0100 (West Africa Standard Time)","error":{"address":"::","code":"EADDRINUSE","errno":-4091,"port":5000,"syscall":"listen"},"exception":true,"level":"error","message":"uncaughtException: listen EADDRINUSE: address already in use :::5000\nError: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1872:16)\n    at listenInCluster (node:net:1920:12)\n    at Server.listen (node:net:2008:7)\n    at startServer (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts:303:31)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","os":{"loadavg":[0,0,0],"uptime":22843.078},"process":{"argv":["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts"],"cwd":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":25344073,"external":28441488,"heapTotal":139001856,"heapUsed":100263200,"rss":194613248},"pid":28208,"uid":null,"version":"v20.11.1"},"stack":"Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1872:16)\n    at listenInCluster (node:net:1920:12)\n    at Server.listen (node:net:2008:7)\n    at startServer (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts:303:31)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-20 18:59:21:5921","trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1872,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1920,"method":null,"native":false},{"column":7,"file":"node:net","function":"Server.listen","line":2008,"method":"listen","native":false},{"column":31,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts","function":"startServer","line":303,"method":null,"native":false},{"column":5,"file":"node:internal/process/task_queues","function":"processTicksAndRejections","line":95,"method":null,"native":false}]}
{"date":"Fri Jun 20 2025 19:00:50 GMT+0100 (West Africa Standard Time)","error":{"address":"::","code":"EADDRINUSE","errno":-4091,"port":5000,"syscall":"listen"},"exception":true,"level":"error","message":"uncaughtException: listen EADDRINUSE: address already in use :::5000\nError: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1872:16)\n    at listenInCluster (node:net:1920:12)\n    at Server.listen (node:net:2008:7)\n    at startServer (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts:303:31)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","os":{"loadavg":[0,0,0],"uptime":22931.5},"process":{"argv":["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts"],"cwd":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":25310094,"external":28407509,"heapTotal":138076160,"heapUsed":87492632,"rss":189734912},"pid":2376,"uid":null,"version":"v20.11.1"},"stack":"Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1872:16)\n    at listenInCluster (node:net:1920:12)\n    at Server.listen (node:net:2008:7)\n    at startServer (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts:303:31)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-20 19:00:50:050","trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1872,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1920,"method":null,"native":false},{"column":7,"file":"node:net","function":"Server.listen","line":2008,"method":"listen","native":false},{"column":31,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts","function":"startServer","line":303,"method":null,"native":false},{"column":5,"file":"node:internal/process/task_queues","function":"processTicksAndRejections","line":95,"method":null,"native":false}]}
{"date":"Fri Jun 20 2025 19:02:21 GMT+0100 (West Africa Standard Time)","error":{"address":"::","code":"EADDRINUSE","errno":-4091,"port":5000,"syscall":"listen"},"exception":true,"level":"error","message":"uncaughtException: listen EADDRINUSE: address already in use :::5000\nError: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1872:16)\n    at listenInCluster (node:net:1920:12)\n    at Server.listen (node:net:2008:7)\n    at startServer (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts:303:31)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","os":{"loadavg":[0,0,0],"uptime":23022.312},"process":{"argv":["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts"],"cwd":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":25393716,"external":28491131,"heapTotal":137814016,"heapUsed":87356792,"rss":186654720},"pid":14072,"uid":null,"version":"v20.11.1"},"stack":"Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1872:16)\n    at listenInCluster (node:net:1920:12)\n    at Server.listen (node:net:2008:7)\n    at startServer (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts:303:31)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-20 19:02:21:221","trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1872,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1920,"method":null,"native":false},{"column":7,"file":"node:net","function":"Server.listen","line":2008,"method":"listen","native":false},{"column":31,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts","function":"startServer","line":303,"method":null,"native":false},{"column":5,"file":"node:internal/process/task_queues","function":"processTicksAndRejections","line":95,"method":null,"native":false}]}
{"date":"Fri Jun 20 2025 19:03:43 GMT+0100 (West Africa Standard Time)","error":{"address":"::","code":"EADDRINUSE","errno":-4091,"port":5000,"syscall":"listen"},"exception":true,"level":"error","message":"uncaughtException: listen EADDRINUSE: address already in use :::5000\nError: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1872:16)\n    at listenInCluster (node:net:1920:12)\n    at Server.listen (node:net:2008:7)\n    at startServer (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts:303:31)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","os":{"loadavg":[0,0,0],"uptime":23104.968},"process":{"argv":["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts"],"cwd":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":25367380,"external":28464795,"heapTotal":134930432,"heapUsed":83986320,"rss":184803328},"pid":19592,"uid":null,"version":"v20.11.1"},"stack":"Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1872:16)\n    at listenInCluster (node:net:1920:12)\n    at Server.listen (node:net:2008:7)\n    at startServer (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts:303:31)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-20 19:03:43:343","trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1872,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1920,"method":null,"native":false},{"column":7,"file":"node:net","function":"Server.listen","line":2008,"method":"listen","native":false},{"column":31,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts","function":"startServer","line":303,"method":null,"native":false},{"column":5,"file":"node:internal/process/task_queues","function":"processTicksAndRejections","line":95,"method":null,"native":false}]}
{"date":"Fri Jun 20 2025 19:17:35 GMT+0100 (West Africa Standard Time)","error":{"address":"::","code":"EADDRINUSE","errno":-4091,"port":5000,"syscall":"listen"},"exception":true,"level":"error","message":"uncaughtException: listen EADDRINUSE: address already in use :::5000\nError: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1872:16)\n    at listenInCluster (node:net:1920:12)\n    at Server.listen (node:net:2008:7)\n    at startServer (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts:303:31)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","os":{"loadavg":[0,0,0],"uptime":23936.656},"process":{"argv":["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts"],"cwd":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":25337001,"external":28434416,"heapTotal":137289728,"heapUsed":79700584,"rss":187047936},"pid":25848,"uid":null,"version":"v20.11.1"},"stack":"Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1872:16)\n    at listenInCluster (node:net:1920:12)\n    at Server.listen (node:net:2008:7)\n    at startServer (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts:303:31)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-20 19:17:35:1735","trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1872,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1920,"method":null,"native":false},{"column":7,"file":"node:net","function":"Server.listen","line":2008,"method":"listen","native":false},{"column":31,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts","function":"startServer","line":303,"method":null,"native":false},{"column":5,"file":"node:internal/process/task_queues","function":"processTicksAndRejections","line":95,"method":null,"native":false}]}
{"date":"Fri Jun 20 2025 19:18:53 GMT+0100 (West Africa Standard Time)","error":{"address":"::","code":"EADDRINUSE","errno":-4091,"port":5000,"syscall":"listen"},"exception":true,"level":"error","message":"uncaughtException: listen EADDRINUSE: address already in use :::5000\nError: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1872:16)\n    at listenInCluster (node:net:1920:12)\n    at Server.listen (node:net:2008:7)\n    at startServer (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts:303:31)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","os":{"loadavg":[0,0,0],"uptime":24015.062},"process":{"argv":["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts"],"cwd":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":25392562,"external":28489977,"heapTotal":137551872,"heapUsed":85762960,"rss":185950208},"pid":12716,"uid":null,"version":"v20.11.1"},"stack":"Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1872:16)\n    at listenInCluster (node:net:1920:12)\n    at Server.listen (node:net:2008:7)\n    at startServer (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts:303:31)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-20 19:18:53:1853","trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1872,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1920,"method":null,"native":false},{"column":7,"file":"node:net","function":"Server.listen","line":2008,"method":"listen","native":false},{"column":31,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts","function":"startServer","line":303,"method":null,"native":false},{"column":5,"file":"node:internal/process/task_queues","function":"processTicksAndRejections","line":95,"method":null,"native":false}]}
{"date":"Fri Jun 20 2025 19:20:08 GMT+0100 (West Africa Standard Time)","error":{"address":"::","code":"EADDRINUSE","errno":-4091,"port":5000,"syscall":"listen"},"exception":true,"level":"error","message":"uncaughtException: listen EADDRINUSE: address already in use :::5000\nError: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1872:16)\n    at listenInCluster (node:net:1920:12)\n    at Server.listen (node:net:2008:7)\n    at startServer (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts:303:31)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","os":{"loadavg":[0,0,0],"uptime":24089.406},"process":{"argv":["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts"],"cwd":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":25347884,"external":28445279,"heapTotal":136085504,"heapUsed":82124208,"rss":182714368},"pid":4056,"uid":null,"version":"v20.11.1"},"stack":"Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1872:16)\n    at listenInCluster (node:net:1920:12)\n    at Server.listen (node:net:2008:7)\n    at startServer (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts:303:31)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-20 19:20:08:208","trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1872,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1920,"method":null,"native":false},{"column":7,"file":"node:net","function":"Server.listen","line":2008,"method":"listen","native":false},{"column":31,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts","function":"startServer","line":303,"method":null,"native":false},{"column":5,"file":"node:internal/process/task_queues","function":"processTicksAndRejections","line":95,"method":null,"native":false}]}
{"date":"Fri Jun 20 2025 19:21:25 GMT+0100 (West Africa Standard Time)","error":{"address":"::","code":"EADDRINUSE","errno":-4091,"port":5000,"syscall":"listen"},"exception":true,"level":"error","message":"uncaughtException: listen EADDRINUSE: address already in use :::5000\nError: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1872:16)\n    at listenInCluster (node:net:1920:12)\n    at Server.listen (node:net:2008:7)\n    at startServer (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts:303:31)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","os":{"loadavg":[0,0,0],"uptime":24166.625},"process":{"argv":["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts"],"cwd":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":7067838,"external":10136885,"heapTotal":147378176,"heapUsed":117564656,"rss":193839104},"pid":9672,"uid":null,"version":"v20.11.1"},"stack":"Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1872:16)\n    at listenInCluster (node:net:1920:12)\n    at Server.listen (node:net:2008:7)\n    at startServer (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts:303:31)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-20 19:21:25:2125","trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1872,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1920,"method":null,"native":false},{"column":7,"file":"node:net","function":"Server.listen","line":2008,"method":"listen","native":false},{"column":31,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts","function":"startServer","line":303,"method":null,"native":false},{"column":5,"file":"node:internal/process/task_queues","function":"processTicksAndRejections","line":95,"method":null,"native":false}]}
{"date":"Fri Jun 20 2025 19:24:07 GMT+0100 (West Africa Standard Time)","error":{"address":"::","code":"EADDRINUSE","errno":-4091,"port":5000,"syscall":"listen"},"exception":true,"level":"error","message":"uncaughtException: listen EADDRINUSE: address already in use :::5000\nError: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1872:16)\n    at listenInCluster (node:net:1920:12)\n    at Server.listen (node:net:2008:7)\n    at startServer (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts:303:31)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","os":{"loadavg":[0,0,0],"uptime":24329.187},"process":{"argv":["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts"],"cwd":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":25369969,"external":28466316,"heapTotal":146309120,"heapUsed":88281232,"rss":194473984},"pid":13736,"uid":null,"version":"v20.11.1"},"stack":"Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1872:16)\n    at listenInCluster (node:net:1920:12)\n    at Server.listen (node:net:2008:7)\n    at startServer (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts:303:31)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-20 19:24:07:247","trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1872,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1920,"method":null,"native":false},{"column":7,"file":"node:net","function":"Server.listen","line":2008,"method":"listen","native":false},{"column":31,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts","function":"startServer","line":303,"method":null,"native":false},{"column":5,"file":"node:internal/process/task_queues","function":"processTicksAndRejections","line":95,"method":null,"native":false}]}
{"date":"Fri Jun 20 2025 19:25:01 GMT+0100 (West Africa Standard Time)","error":{"address":"::","code":"EADDRINUSE","errno":-4091,"port":5000,"syscall":"listen"},"exception":true,"level":"error","message":"uncaughtException: listen EADDRINUSE: address already in use :::5000\nError: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1872:16)\n    at listenInCluster (node:net:1920:12)\n    at Server.listen (node:net:2008:7)\n    at startServer (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts:303:31)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","os":{"loadavg":[0,0,0],"uptime":24382.531},"process":{"argv":["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts"],"cwd":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":25355012,"external":28452427,"heapTotal":134668288,"heapUsed":96137904,"rss":185757696},"pid":11084,"uid":null,"version":"v20.11.1"},"stack":"Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1872:16)\n    at listenInCluster (node:net:1920:12)\n    at Server.listen (node:net:2008:7)\n    at startServer (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts:303:31)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-20 19:25:01:251","trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1872,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1920,"method":null,"native":false},{"column":7,"file":"node:net","function":"Server.listen","line":2008,"method":"listen","native":false},{"column":31,"file":"C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts","function":"startServer","line":303,"method":null,"native":false},{"column":5,"file":"node:internal/process/task_queues","function":"processTicksAndRejections","line":95,"method":null,"native":false}]}
