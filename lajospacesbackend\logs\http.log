{"code":"ESOCKET","command":"CONN","level":"error","library":"SSL routines","message":"Email transporter configuration error: 084A0000:error:0A00010B:SSL routines:ssl3_get_record:wrong version number:c:\\ws\\deps\\openssl\\openssl\\ssl\\record\\ssl3_record.c:355:\n","reason":"wrong version number","stack":"Error: 084A0000:error:0A00010B:SSL routines:ssl3_get_record:wrong version number:c:\\ws\\deps\\openssl\\openssl\\ssl\\record\\ssl3_record.c:355:\n","timestamp":"2025-06-20 13:09:34:934"}
{"code":"ESOCKET","command":"CONN","level":"error","library":"SSL routines","message":"Email transporter configuration error: E0550000:error:0A00010B:SSL routines:ssl3_get_record:wrong version number:c:\\ws\\deps\\openssl\\openssl\\ssl\\record\\ssl3_record.c:355:\n","reason":"wrong version number","stack":"Error: E0550000:error:0A00010B:SSL routines:ssl3_get_record:wrong version number:c:\\ws\\deps\\openssl\\openssl\\ssl\\record\\ssl3_record.c:355:\n","timestamp":"2025-06-20 13:13:44:1344"}
{"level":"info","message":"✅ Email service ready","timestamp":"2025-06-20 13:49:29:4929"}
{"level":"info","message":"✅ Email service ready","timestamp":"2025-06-20 13:57:33:5733"}
{"level":"info","message":"✅ Email service ready","timestamp":"2025-06-20 13:58:57:5857"}
{"level":"info","message":"✅ Email service ready","timestamp":"2025-06-20 14:00:16:016"}
{"level":"info","message":"✅ Email service ready","timestamp":"2025-06-20 14:03:10:310"}
{"level":"info","message":"✅ Email service ready","timestamp":"2025-06-20 14:05:58:558"}
{"level":"info","message":"✅ Email service ready","timestamp":"2025-06-20 14:16:23:1623"}
{"level":"info","message":"✅ Email service ready","timestamp":"2025-06-20 14:34:49:3449"}
{"level":"info","message":"✅ Email service ready","timestamp":"2025-06-20 14:54:44:5444"}
{"level":"info","message":"🚀 Starting server initialization...","timestamp":"2025-06-20 14:57:18:5718"}
{"level":"info","message":"🚀 Starting LajoSpaces Backend Server...","timestamp":"2025-06-20 14:57:18:5718"}
{"level":"info","message":"📊 Connecting to MongoDB...","timestamp":"2025-06-20 14:57:18:5718"}
{"level":"info","message":"📊 Connecting to MongoDB...","timestamp":"2025-06-20 14:57:18:5718"}
{"level":"info","message":"✅ Email service ready","timestamp":"2025-06-20 14:57:23:5723"}
{"level":"info","message":"📊 Mongoose connected to MongoDB","timestamp":"2025-06-20 14:57:23:5723"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-20 14:57:23:5723"}
{"level":"info","message":"📊 Connected to database: lajospaces","timestamp":"2025-06-20 14:57:23:5723"}
{"level":"info","message":"✅ MongoDB connection completed","timestamp":"2025-06-20 14:57:23:5723"}
{"level":"info","message":"🔴 Connecting to Redis...","timestamp":"2025-06-20 14:57:23:5723"}
{"level":"info","message":"🔴 Connecting to Redis...","timestamp":"2025-06-20 14:57:23:5723"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 14:57:24:5724"}
{"level":"info","message":"✅ Redis client ready","timestamp":"2025-06-20 14:57:24:5724"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-20 14:57:25:5725"}
{"level":"info","message":"✅ Redis connection completed","timestamp":"2025-06-20 14:57:25:5725"}
{"level":"info","message":"⚙️ Initializing services...","timestamp":"2025-06-20 14:57:25:5725"}
{"level":"info","message":"All services initialized successfully (services temporarily disabled)","timestamp":"2025-06-20 14:57:25:5725"}
{"level":"info","message":"✅ Services initialization completed","timestamp":"2025-06-20 14:57:25:5725"}
{"level":"info","message":"🛡️ Setting up rate limiting...","timestamp":"2025-06-20 14:57:25:5725"}
{"level":"info","message":"💬 Initializing Socket.IO service...","timestamp":"2025-06-20 14:57:25:5725"}
{"level":"info","message":"✅ Socket.IO service initialized","timestamp":"2025-06-20 14:57:25:5725"}
{"level":"info","message":"🚀 LajoSpaces Backend Server running on port 5000","timestamp":"2025-06-20 14:57:25:5725"}
{"level":"info","message":"🌍 Environment: development","timestamp":"2025-06-20 14:57:25:5725"}
{"level":"info","message":"📊 Health check: http://localhost:5000/health","timestamp":"2025-06-20 14:57:25:5725"}
{"level":"info","message":"🔗 API Base URL: http://localhost:5000/api","timestamp":"2025-06-20 14:57:25:5725"}
{"level":"info","message":"💬 Socket.IO enabled for real-time messaging","timestamp":"2025-06-20 14:57:25:5725"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 14:57:40:5740"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 14:57:43:5743"}
{"level":"info","message":"✅ Redis client ready","timestamp":"2025-06-20 14:57:43:5743"}
{"level":"info","message":"🚀 Starting server initialization...","timestamp":"2025-06-20 14:58:43:5843"}
{"level":"info","message":"🚀 Starting LajoSpaces Backend Server...","timestamp":"2025-06-20 14:58:43:5843"}
{"level":"info","message":"📊 Connecting to MongoDB...","timestamp":"2025-06-20 14:58:43:5843"}
{"level":"info","message":"📊 Connecting to MongoDB...","timestamp":"2025-06-20 14:58:43:5843"}
{"level":"info","message":"✅ Email service ready","timestamp":"2025-06-20 14:58:46:5846"}
{"level":"info","message":"📊 Mongoose connected to MongoDB","timestamp":"2025-06-20 14:58:46:5846"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-20 14:58:46:5846"}
{"level":"info","message":"📊 Connected to database: lajospaces","timestamp":"2025-06-20 14:58:46:5846"}
{"level":"info","message":"✅ MongoDB connection completed","timestamp":"2025-06-20 14:58:46:5846"}
{"level":"info","message":"🔴 Connecting to Redis...","timestamp":"2025-06-20 14:58:46:5846"}
{"level":"info","message":"🔴 Connecting to Redis...","timestamp":"2025-06-20 14:58:46:5846"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 14:58:46:5846"}
{"level":"info","message":"✅ Redis client ready","timestamp":"2025-06-20 14:58:47:5847"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-20 14:58:47:5847"}
{"level":"info","message":"✅ Redis connection completed","timestamp":"2025-06-20 14:58:47:5847"}
{"level":"info","message":"⚙️ Initializing services...","timestamp":"2025-06-20 14:58:47:5847"}
{"level":"info","message":"All services initialized successfully (services temporarily disabled)","timestamp":"2025-06-20 14:58:47:5847"}
{"level":"info","message":"✅ Services initialization completed","timestamp":"2025-06-20 14:58:47:5847"}
{"level":"info","message":"🛡️ Setting up rate limiting...","timestamp":"2025-06-20 14:58:47:5847"}
{"level":"info","message":"💬 Initializing Socket.IO service...","timestamp":"2025-06-20 14:58:47:5847"}
{"level":"info","message":"✅ Socket.IO service initialized","timestamp":"2025-06-20 14:58:47:5847"}
{"level":"info","message":"SIGINT received, shutting down gracefully","timestamp":"2025-06-20 14:59:00:590"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 15:00:10:010"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 15:00:15:015"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 15:00:20:020"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 15:00:25:025"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 15:00:31:031"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 15:00:36:036"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 15:00:42:042"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 15:00:47:047"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 15:00:53:053"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 15:00:58:058"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 15:01:04:14"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 15:01:05:15"}
{"level":"info","message":"✅ Redis client ready","timestamp":"2025-06-20 15:01:05:15"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 15:07:43:743"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 15:07:49:749"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 15:07:52:752"}
{"level":"info","message":"✅ Redis client ready","timestamp":"2025-06-20 15:07:53:753"}
{"level":"info","message":"::1 - - [20/Jun/2025:14:12:11 +0000] \"GET /api/docs HTTP/1.1\" 301 158 \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","timestamp":"2025-06-20 15:12:11:1211"}
{"level":"info","message":"::1 - - [20/Jun/2025:14:12:11 +0000] \"GET /api/docs/ HTTP/1.1\" 200 - \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","timestamp":"2025-06-20 15:12:11:1211"}
{"level":"info","message":"::1 - - [20/Jun/2025:14:12:11 +0000] \"GET /api/docs/swagger-ui-init.js HTTP/1.1\" 200 - \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","timestamp":"2025-06-20 15:12:11:1211"}
{"level":"info","message":"::1 - - [20/Jun/2025:14:12:11 +0000] \"GET /api/docs/swagger-ui.css HTTP/1.1\" 200 - \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","timestamp":"2025-06-20 15:12:11:1211"}
{"level":"info","message":"::1 - - [20/Jun/2025:14:12:11 +0000] \"GET /api/docs/swagger-ui-standalone-preset.js HTTP/1.1\" 200 - \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","timestamp":"2025-06-20 15:12:11:1211"}
{"level":"info","message":"::1 - - [20/Jun/2025:14:12:11 +0000] \"GET /api/docs/swagger-ui-bundle.js HTTP/1.1\" 200 - \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","timestamp":"2025-06-20 15:12:11:1211"}
{"error":{"message":"Route /favicon.ico not found on this server","name":"Error","stack":"Error: Route /favicon.ico not found on this server\n    at notFoundHandler (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\middleware\\notFoundHandler.ts:9:17)\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at logger (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\morgan\\index.js:144:5)\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)"},"level":"error","message":"Error 404: Route /favicon.ico not found on this server","request":{"ip":"::1","method":"GET","url":"/favicon.ico","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"},"timestamp":"2025-06-20 15:12:12:1212","user":null}
{"level":"info","message":"::1 - - [20/Jun/2025:14:12:12 +0000] \"GET /favicon.ico HTTP/1.1\" 404 332 \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","timestamp":"2025-06-20 15:12:12:1212"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 15:22:56:2256"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 15:23:02:232"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 15:23:07:237"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 15:23:12:2312"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 15:23:13:2313"}
{"level":"info","message":"✅ Redis client ready","timestamp":"2025-06-20 15:23:13:2313"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 15:24:58:2458"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 15:25:01:251"}
{"level":"info","message":"✅ Redis client ready","timestamp":"2025-06-20 15:25:02:252"}
{"level":"info","message":"::1 - - [20/Jun/2025:14:26:52 +0000] \"GET /api HTTP/1.1\" 200 502 \"-\" \"curl/8.6.0\"","timestamp":"2025-06-20 15:26:52:2652"}
{"level":"info","message":"::1 - - [20/Jun/2025:14:27:31 +0000] \"GET /api HTTP/1.1\" 200 502 \"-\" \"curl/8.6.0\"","timestamp":"2025-06-20 15:27:31:2731"}
{"level":"info","message":"::1 - - [20/Jun/2025:14:28:10 +0000] \"GET /api/auth/health HTTP/1.1\" 200 211 \"-\" \"curl/8.6.0\"","timestamp":"2025-06-20 15:28:10:2810"}
{"error":{"message":"Route /api/properties/health not found on this server","name":"Error","stack":"Error: Route /api/properties/health not found on this server\n    at notFoundHandler (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\middleware\\notFoundHandler.ts:9:17)\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at logger (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\morgan\\index.js:144:5)\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)"},"level":"error","message":"Error 404: Route /api/properties/health not found on this server","request":{"ip":"::1","method":"GET","url":"/api/properties/health","userAgent":"curl/8.6.0"},"timestamp":"2025-06-20 15:32:30:3230","user":null}
{"level":"info","message":"::1 - - [20/Jun/2025:14:32:30 +0000] \"GET /api/properties/health HTTP/1.1\" 404 362 \"-\" \"curl/8.6.0\"","timestamp":"2025-06-20 15:32:30:3230"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 15:32:57:3257"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 15:33:02:332"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 15:33:07:337"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 15:33:13:3313"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 15:33:18:3318"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 15:33:23:3323"}
{"level":"info","message":"✅ Redis client ready","timestamp":"2025-06-20 15:33:23:3323"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 15:34:44:3444"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 15:34:46:3446"}
{"level":"info","message":"✅ Redis client ready","timestamp":"2025-06-20 15:34:46:3446"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 15:40:20:4020"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 15:40:25:4025"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 15:40:31:4031"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 15:40:36:4036"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 15:40:40:4040"}
{"level":"info","message":"✅ Redis client ready","timestamp":"2025-06-20 15:40:40:4040"}
{"level":"info","message":"✅ Email service ready","timestamp":"2025-06-20 15:40:46:4046"}
{"level":"info","message":"✅ Email service ready","timestamp":"2025-06-20 15:43:49:4349"}
{"level":"info","message":"✅ Email service ready","timestamp":"2025-06-20 16:07:46:746"}
{"level":"info","message":"✅ Email service ready","timestamp":"2025-06-20 16:08:53:853"}
{"level":"info","message":"✅ Email service ready","timestamp":"2025-06-20 16:13:17:1317"}
{"level":"info","message":"📧 Email service initialized (verification disabled for testing)","timestamp":"2025-06-20 16:17:22:1722"}
{"level":"info","message":"📧 Email service initialized (verification disabled for testing)","timestamp":"2025-06-20 16:18:27:1827"}
{"level":"info","message":"🚀 Starting server initialization...","timestamp":"2025-06-20 16:18:28:1828"}
{"level":"info","message":"🚀 Starting LajoSpaces Backend Server...","timestamp":"2025-06-20 16:18:28:1828"}
{"level":"info","message":"📊 Connecting to MongoDB...","timestamp":"2025-06-20 16:18:28:1828"}
{"level":"info","message":"📊 Connecting to MongoDB...","timestamp":"2025-06-20 16:18:28:1828"}
{"level":"info","message":"📊 Mongoose connected to MongoDB","timestamp":"2025-06-20 16:18:31:1831"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-20 16:18:31:1831"}
{"level":"info","message":"📊 Connected to database: lajospaces","timestamp":"2025-06-20 16:18:31:1831"}
{"level":"info","message":"✅ MongoDB connection completed","timestamp":"2025-06-20 16:18:31:1831"}
{"level":"info","message":"🔴 Connecting to Redis...","timestamp":"2025-06-20 16:18:31:1831"}
{"level":"info","message":"🔴 Connecting to Redis...","timestamp":"2025-06-20 16:18:31:1831"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 16:18:31:1831"}
{"level":"info","message":"✅ Redis client ready","timestamp":"2025-06-20 16:18:31:1831"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-20 16:18:32:1832"}
{"level":"info","message":"✅ Redis connection completed","timestamp":"2025-06-20 16:18:32:1832"}
{"level":"info","message":"⚙️ Initializing services...","timestamp":"2025-06-20 16:18:32:1832"}
{"level":"info","message":"All services initialized successfully (temporarily minimal)","timestamp":"2025-06-20 16:18:32:1832"}
{"level":"info","message":"✅ Services initialization completed","timestamp":"2025-06-20 16:18:32:1832"}
{"level":"info","message":"💬 Initializing Socket.IO service...","timestamp":"2025-06-20 16:18:32:1832"}
{"level":"info","message":"✅ Socket.IO service initialized","timestamp":"2025-06-20 16:18:32:1832"}
{"level":"info","message":"🚀 LajoSpaces Backend Server running on port 5000","timestamp":"2025-06-20 16:18:32:1832"}
{"level":"info","message":"🌍 Environment: development","timestamp":"2025-06-20 16:18:32:1832"}
{"level":"info","message":"📊 Health check: http://localhost:5000/health","timestamp":"2025-06-20 16:18:32:1832"}
{"level":"info","message":"🔗 API Base URL: http://localhost:5000/api","timestamp":"2025-06-20 16:18:32:1832"}
{"level":"info","message":"💬 Socket.IO enabled for real-time messaging","timestamp":"2025-06-20 16:18:32:1832"}
{"level":"info","message":"::1 - - [20/Jun/2025:15:20:06 +0000] \"GET /api HTTP/1.1\" 200 502 \"-\" \"curl/8.6.0\"","timestamp":"2025-06-20 16:20:06:206"}
{"level":"info","message":"::1 - - [20/Jun/2025:15:20:17 +0000] \"GET /api/auth/health HTTP/1.1\" 200 211 \"-\" \"curl/8.6.0\"","timestamp":"2025-06-20 16:20:17:2017"}
{"level":"info","message":"📧 Email service initialized (verification disabled for testing)","timestamp":"2025-06-20 16:21:39:2139"}
{"level":"info","message":"📧 Email service initialized (verification disabled for testing)","timestamp":"2025-06-20 16:24:07:247"}
{"level":"info","message":"🚀 Starting server initialization...","timestamp":"2025-06-20 16:24:08:248"}
{"level":"info","message":"🚀 Starting LajoSpaces Backend Server...","timestamp":"2025-06-20 16:24:08:248"}
{"level":"info","message":"📊 Connecting to MongoDB...","timestamp":"2025-06-20 16:24:08:248"}
{"level":"info","message":"📊 Connecting to MongoDB...","timestamp":"2025-06-20 16:24:08:248"}
{"level":"info","message":"📊 Mongoose disconnected from MongoDB","timestamp":"2025-06-20 16:24:14:2414"}
{"level":"info","message":"📧 Email service initialized (verification disabled for testing)","timestamp":"2025-06-20 16:25:15:2515"}
{"level":"info","message":"🚀 Starting server initialization...","timestamp":"2025-06-20 16:25:17:2517"}
{"level":"info","message":"🚀 Starting LajoSpaces Backend Server...","timestamp":"2025-06-20 16:25:17:2517"}
{"level":"info","message":"📊 Connecting to MongoDB...","timestamp":"2025-06-20 16:25:17:2517"}
{"level":"info","message":"📊 Connecting to MongoDB...","timestamp":"2025-06-20 16:25:17:2517"}
{"level":"info","message":"📊 Mongoose connected to MongoDB","timestamp":"2025-06-20 16:25:20:2520"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-20 16:25:20:2520"}
{"level":"info","message":"📊 Connected to database: lajospaces","timestamp":"2025-06-20 16:25:20:2520"}
{"level":"info","message":"✅ MongoDB connection completed","timestamp":"2025-06-20 16:25:20:2520"}
{"level":"info","message":"🔴 Connecting to Redis...","timestamp":"2025-06-20 16:25:20:2520"}
{"level":"info","message":"🔴 Connecting to Redis...","timestamp":"2025-06-20 16:25:20:2520"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 16:25:21:2521"}
{"level":"info","message":"✅ Redis client ready","timestamp":"2025-06-20 16:25:21:2521"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-20 16:25:21:2521"}
{"level":"info","message":"✅ Redis connection completed","timestamp":"2025-06-20 16:25:21:2521"}
{"level":"info","message":"⚙️ Initializing services...","timestamp":"2025-06-20 16:25:21:2521"}
{"level":"info","message":"All services initialized successfully (temporarily minimal)","timestamp":"2025-06-20 16:25:21:2521"}
{"level":"info","message":"✅ Services initialization completed","timestamp":"2025-06-20 16:25:21:2521"}
{"level":"info","message":"💬 Initializing Socket.IO service...","timestamp":"2025-06-20 16:25:21:2521"}
{"level":"info","message":"✅ Socket.IO service initialized","timestamp":"2025-06-20 16:25:21:2521"}
{"level":"info","message":"🚀 LajoSpaces Backend Server running on port 5000","timestamp":"2025-06-20 16:25:21:2521"}
{"level":"info","message":"🌍 Environment: development","timestamp":"2025-06-20 16:25:21:2521"}
{"level":"info","message":"📊 Health check: http://localhost:5000/health","timestamp":"2025-06-20 16:25:21:2521"}
{"level":"info","message":"🔗 API Base URL: http://localhost:5000/api","timestamp":"2025-06-20 16:25:21:2521"}
{"level":"info","message":"💬 Socket.IO enabled for real-time messaging","timestamp":"2025-06-20 16:25:21:2521"}
{"level":"info","message":"::1 - - [20/Jun/2025:15:26:06 +0000] \"GET /api/profiles/health HTTP/1.1\" 200 237 \"-\" \"curl/8.6.0\"","timestamp":"2025-06-20 16:26:06:266"}
{"level":"info","message":"📧 Email service initialized (verification disabled for testing)","timestamp":"2025-06-20 16:34:58:3458"}
{"level":"info","message":"📧 Email service initialized (verification disabled for testing)","timestamp":"2025-06-20 16:37:50:3750"}
{"level":"info","message":"📧 Email service initialized (verification disabled for testing)","timestamp":"2025-06-20 16:44:05:445"}
{"level":"info","message":"📧 Email service initialized (verification disabled for testing)","timestamp":"2025-06-20 16:49:49:4949"}
{"level":"info","message":"📧 Email service initialized (verification disabled for testing)","timestamp":"2025-06-20 16:55:18:5518"}
{"level":"info","message":"📧 Email service initialized (verification disabled for testing)","timestamp":"2025-06-20 16:57:38:5738"}
{"level":"info","message":"📧 Email service initialized (verification disabled for testing)","timestamp":"2025-06-20 16:58:44:5844"}
{"level":"info","message":"🚀 Starting server initialization...","timestamp":"2025-06-20 17:37:20:3720"}
{"level":"info","message":"🚀 Starting LajoSpaces Backend Server...","timestamp":"2025-06-20 17:37:20:3720"}
{"level":"info","message":"⚙️ Initializing services...","timestamp":"2025-06-20 17:37:20:3720"}
{"level":"info","message":"✅ Services initialization completed (skipped for testing)","timestamp":"2025-06-20 17:37:20:3720"}
{"level":"info","message":"🛡️ Setting up rate limiting...","timestamp":"2025-06-20 17:37:20:3720"}
{"level":"info","message":"🚀 LajoSpaces Backend Server running on port 5000","timestamp":"2025-06-20 17:37:20:3720"}
{"level":"info","message":"🌍 Environment: development","timestamp":"2025-06-20 17:37:20:3720"}
{"level":"info","message":"📊 Health check: http://localhost:5000/health","timestamp":"2025-06-20 17:37:20:3720"}
{"level":"info","message":"🔗 API Base URL: http://localhost:5000/api","timestamp":"2025-06-20 17:37:20:3720"}
{"level":"info","message":"⚠️ Socket.IO temporarily disabled for testing","timestamp":"2025-06-20 17:37:20:3720"}
{"level":"info","message":"::1 - - [20/Jun/2025:16:43:30 +0000] \"GET /health HTTP/1.1\" 200 233 \"-\" \"curl/8.6.0\"","timestamp":"2025-06-20 17:43:30:4330"}
{"level":"info","message":"🚀 Starting server initialization...","timestamp":"2025-06-20 17:56:26:5626"}
{"level":"info","message":"🚀 Starting LajoSpaces Backend Server...","timestamp":"2025-06-20 17:56:26:5626"}
{"level":"info","message":"📊 Connecting to MongoDB...","timestamp":"2025-06-20 17:56:26:5626"}
{"level":"info","message":"📊 Connecting to MongoDB...","timestamp":"2025-06-20 17:56:26:5626"}
{"level":"info","message":"📊 Mongoose connected to MongoDB","timestamp":"2025-06-20 17:56:28:5628"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-20 17:56:28:5628"}
{"level":"info","message":"📊 Connected to database: lajospaces","timestamp":"2025-06-20 17:56:28:5628"}
{"level":"info","message":"✅ MongoDB connection completed","timestamp":"2025-06-20 17:56:28:5628"}
{"level":"info","message":"🔴 Connecting to Redis...","timestamp":"2025-06-20 17:56:28:5628"}
{"level":"info","message":"🔴 Connecting to Redis...","timestamp":"2025-06-20 17:56:28:5628"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 17:56:29:5629"}
{"level":"info","message":"✅ Redis client ready","timestamp":"2025-06-20 17:56:29:5629"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-20 17:56:29:5629"}
{"level":"info","message":"🚀 Starting server initialization...","timestamp":"2025-06-20 17:57:20:5720"}
{"level":"info","message":"🚀 Starting LajoSpaces Backend Server...","timestamp":"2025-06-20 17:57:20:5720"}
{"level":"info","message":"📊 Connecting to MongoDB...","timestamp":"2025-06-20 17:57:20:5720"}
{"level":"info","message":"📊 Connecting to MongoDB...","timestamp":"2025-06-20 17:57:20:5720"}
{"level":"info","message":"📊 Mongoose connected to MongoDB","timestamp":"2025-06-20 17:57:24:5724"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-20 17:57:24:5724"}
{"level":"info","message":"📊 Connected to database: lajospaces","timestamp":"2025-06-20 17:57:24:5724"}
{"level":"info","message":"✅ MongoDB connection completed","timestamp":"2025-06-20 17:57:24:5724"}
{"level":"info","message":"🔴 Connecting to Redis...","timestamp":"2025-06-20 17:57:24:5724"}
{"level":"info","message":"🔴 Connecting to Redis...","timestamp":"2025-06-20 17:57:24:5724"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 17:57:24:5724"}
{"level":"info","message":"✅ Redis client ready","timestamp":"2025-06-20 17:57:24:5724"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-20 17:57:24:5724"}
{"level":"info","message":"🚀 Starting server initialization...","timestamp":"2025-06-20 17:59:37:5937"}
{"level":"info","message":"🚀 Starting LajoSpaces Backend Server...","timestamp":"2025-06-20 17:59:37:5937"}
{"level":"info","message":"📊 Connecting to MongoDB...","timestamp":"2025-06-20 17:59:37:5937"}
{"level":"info","message":"📊 Connecting to MongoDB...","timestamp":"2025-06-20 17:59:37:5937"}
{"level":"info","message":"📊 Mongoose connected to MongoDB","timestamp":"2025-06-20 17:59:40:5940"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-20 17:59:40:5940"}
{"level":"info","message":"📊 Connected to database: lajospaces","timestamp":"2025-06-20 17:59:40:5940"}
{"level":"info","message":"✅ MongoDB connection completed","timestamp":"2025-06-20 17:59:40:5940"}
{"level":"info","message":"🔴 Connecting to Redis...","timestamp":"2025-06-20 17:59:40:5940"}
{"level":"info","message":"🔴 Connecting to Redis...","timestamp":"2025-06-20 17:59:40:5940"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 17:59:41:5941"}
{"level":"info","message":"✅ Redis client ready","timestamp":"2025-06-20 17:59:41:5941"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-20 17:59:41:5941"}
{"level":"info","message":"✅ Redis connection completed","timestamp":"2025-06-20 17:59:41:5941"}
{"level":"info","message":"📋 Setting up API routes...","timestamp":"2025-06-20 17:59:41:5941"}
{"level":"info","message":"📧 Email service initialized (verification disabled for testing)","timestamp":"2025-06-20 17:59:42:5942"}
{"level":"info","message":"Email templates initialized successfully","timestamp":"2025-06-20 17:59:43:5943"}
{"level":"info","message":"✅ API routes configured","timestamp":"2025-06-20 17:59:43:5943"}
{"level":"info","message":"⚙️ Initializing services...","timestamp":"2025-06-20 17:59:43:5943"}
{"level":"info","message":"🔄 Connecting cache service...","timestamp":"2025-06-20 17:59:43:5943"}
{"level":"info","message":"Redis cache client connecting...","timestamp":"2025-06-20 17:59:43:5943"}
{"level":"info","message":"Redis cache client connected and ready","timestamp":"2025-06-20 17:59:43:5943"}
{"level":"info","message":"✅ Cache service connected","timestamp":"2025-06-20 17:59:43:5943"}
{"level":"info","message":"🔄 Connecting session service...","timestamp":"2025-06-20 17:59:43:5943"}
{"level":"info","message":"Redis session client connecting...","timestamp":"2025-06-20 17:59:43:5943"}
{"level":"info","message":"Redis session client connected and ready","timestamp":"2025-06-20 17:59:44:5944"}
{"level":"info","message":"✅ Session service connected","timestamp":"2025-06-20 17:59:44:5944"}
{"level":"info","message":"🔄 Connecting token service...","timestamp":"2025-06-20 17:59:44:5944"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 17:59:44:5944"}
{"level":"info","message":"Redis token client connected and ready","timestamp":"2025-06-20 17:59:44:5944"}
{"level":"info","message":"✅ Token service connected","timestamp":"2025-06-20 17:59:44:5944"}
{"level":"info","message":"All services initialized successfully","timestamp":"2025-06-20 17:59:44:5944"}
{"level":"info","message":"✅ Services initialization completed","timestamp":"2025-06-20 17:59:44:5944"}
{"level":"info","message":"🔐 Setting up session middleware...","timestamp":"2025-06-20 17:59:44:5944"}
{"level":"info","message":"🛡️ Setting up rate limiting...","timestamp":"2025-06-20 17:59:44:5944"}
{"level":"info","message":"💬 Initializing Socket.IO service...","timestamp":"2025-06-20 17:59:44:5944"}
{"level":"info","message":"✅ Socket.IO service initialized","timestamp":"2025-06-20 17:59:44:5944"}
{"level":"info","message":"🚀 LajoSpaces Backend Server running on port 5000","timestamp":"2025-06-20 17:59:44:5944"}
{"level":"info","message":"🌍 Environment: development","timestamp":"2025-06-20 17:59:44:5944"}
{"level":"info","message":"📊 Health check: http://localhost:5000/health","timestamp":"2025-06-20 17:59:44:5944"}
{"level":"info","message":"🔗 API Base URL: http://localhost:5000/api","timestamp":"2025-06-20 17:59:44:5944"}
{"level":"info","message":"💬 Socket.IO enabled for real-time messaging","timestamp":"2025-06-20 17:59:44:5944"}
{"level":"info","message":"::1 - - [20/Jun/2025:17:00:32 +0000] \"GET /health HTTP/1.1\" 200 221 \"-\" \"curl/8.6.0\"","timestamp":"2025-06-20 18:00:32:032"}
{"error":{"message":"Route /api/auth/health not found on this server","name":"Error","stack":"Error: Route /api/auth/health not found on this server\n    at notFoundHandler (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\middleware\\notFoundHandler.ts:9:17)\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts:180:3\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)"},"level":"error","message":"Error 404: Route /api/auth/health not found on this server","request":{"ip":"::1","method":"GET","url":"/api/auth/health","userAgent":"curl/8.6.0"},"timestamp":"2025-06-20 18:00:37:037","user":null}
{"level":"info","message":"::1 - - [20/Jun/2025:17:00:37 +0000] \"GET /api/auth/health HTTP/1.1\" 404 344 \"-\" \"curl/8.6.0\"","timestamp":"2025-06-20 18:00:37:037"}
{"endpoint":"/api/auth/health","eventType":"data_viewed","ipAddress":"::1","level":"info","message":"Audit event logged","success":false,"timestamp":"2025-06-20 18:00:37:037"}
{"error":{"message":"Route /api/auth/health not found on this server","name":"Error","stack":"Error: Route /api/auth/health not found on this server\n    at notFoundHandler (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\middleware\\notFoundHandler.ts:9:17)\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts:180:3\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)"},"level":"error","message":"Error 404: Route /api/auth/health not found on this server","request":{"ip":"::1","method":"GET","url":"/api/auth/health","userAgent":"curl/8.6.0"},"timestamp":"2025-06-20 18:00:51:051","user":null}
{"level":"info","message":"::1 - - [20/Jun/2025:17:00:51 +0000] \"GET /api/auth/health HTTP/1.1\" 404 344 \"-\" \"curl/8.6.0\"","timestamp":"2025-06-20 18:00:51:051"}
{"endpoint":"/api/auth/health","eventType":"data_viewed","ipAddress":"::1","level":"info","message":"Audit event logged","success":false,"timestamp":"2025-06-20 18:00:51:051"}
{"level":"info","message":"::1 - - [20/Jun/2025:17:00:59 +0000] \"GET /api HTTP/1.1\" 200 502 \"-\" \"curl/8.6.0\"","timestamp":"2025-06-20 18:00:59:059"}
{"error":{"message":"Route /api/auth/health not found on this server","name":"Error","stack":"Error: Route /api/auth/health not found on this server\n    at notFoundHandler (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\middleware\\notFoundHandler.ts:9:17)\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts:180:3\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)"},"level":"error","message":"Error 404: Route /api/auth/health not found on this server","request":{"ip":"::1","method":"GET","url":"/api/auth/health","userAgent":"curl/8.6.0"},"timestamp":"2025-06-20 18:01:08:18","user":null}
{"level":"info","message":"::1 - - [20/Jun/2025:17:01:08 +0000] \"GET /api/auth/health HTTP/1.1\" 404 344 \"-\" \"curl/8.6.0\"","timestamp":"2025-06-20 18:01:08:18"}
{"endpoint":"/api/auth/health","eventType":"data_viewed","ipAddress":"::1","level":"info","message":"Audit event logged","success":false,"timestamp":"2025-06-20 18:01:09:19"}
{"error":{"message":"Route /api/users/health not found on this server","name":"Error","stack":"Error: Route /api/users/health not found on this server\n    at notFoundHandler (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\middleware\\notFoundHandler.ts:9:17)\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts:180:3\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)"},"level":"error","message":"Error 404: Route /api/users/health not found on this server","request":{"ip":"::1","method":"GET","url":"/api/users/health","userAgent":"curl/8.6.0"},"timestamp":"2025-06-20 18:01:28:128","user":null}
{"level":"info","message":"::1 - - [20/Jun/2025:17:01:28 +0000] \"GET /api/users/health HTTP/1.1\" 404 347 \"-\" \"curl/8.6.0\"","timestamp":"2025-06-20 18:01:28:128"}
{"endpoint":"/api/users/health","eventType":"data_viewed","ipAddress":"::1","level":"info","message":"Audit event logged","success":false,"timestamp":"2025-06-20 18:01:28:128"}
{"level":"info","message":"🚀 Starting server initialization...","timestamp":"2025-06-20 18:02:51:251"}
{"level":"info","message":"🚀 Starting LajoSpaces Backend Server...","timestamp":"2025-06-20 18:02:51:251"}
{"level":"info","message":"📊 Connecting to MongoDB...","timestamp":"2025-06-20 18:02:51:251"}
{"level":"info","message":"📊 Connecting to MongoDB...","timestamp":"2025-06-20 18:02:51:251"}
{"level":"info","message":"📊 Mongoose connected to MongoDB","timestamp":"2025-06-20 18:02:54:254"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-20 18:02:54:254"}
{"level":"info","message":"📊 Connected to database: lajospaces","timestamp":"2025-06-20 18:02:54:254"}
{"level":"info","message":"✅ MongoDB connection completed","timestamp":"2025-06-20 18:02:54:254"}
{"level":"info","message":"🔴 Connecting to Redis...","timestamp":"2025-06-20 18:02:54:254"}
{"level":"info","message":"🔴 Connecting to Redis...","timestamp":"2025-06-20 18:02:54:254"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:02:54:254"}
{"level":"info","message":"✅ Redis client ready","timestamp":"2025-06-20 18:02:54:254"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-20 18:02:54:254"}
{"level":"info","message":"✅ Redis connection completed","timestamp":"2025-06-20 18:02:54:254"}
{"level":"info","message":"⚙️ Initializing services...","timestamp":"2025-06-20 18:02:54:254"}
{"level":"info","message":"🔄 Connecting cache service...","timestamp":"2025-06-20 18:02:54:254"}
{"level":"info","message":"Redis cache client connecting...","timestamp":"2025-06-20 18:02:55:255"}
{"level":"info","message":"Redis cache client connected and ready","timestamp":"2025-06-20 18:02:55:255"}
{"level":"info","message":"✅ Cache service connected","timestamp":"2025-06-20 18:02:55:255"}
{"level":"info","message":"🔄 Connecting session service...","timestamp":"2025-06-20 18:02:55:255"}
{"level":"info","message":"Redis session client connecting...","timestamp":"2025-06-20 18:02:56:256"}
{"level":"info","message":"Redis session client connected and ready","timestamp":"2025-06-20 18:02:56:256"}
{"level":"info","message":"✅ Session service connected","timestamp":"2025-06-20 18:02:56:256"}
{"level":"info","message":"🔄 Connecting token service...","timestamp":"2025-06-20 18:02:56:256"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:02:57:257"}
{"level":"info","message":"Redis token client connected and ready","timestamp":"2025-06-20 18:02:57:257"}
{"level":"info","message":"✅ Token service connected","timestamp":"2025-06-20 18:02:57:257"}
{"level":"info","message":"All services initialized successfully","timestamp":"2025-06-20 18:02:57:257"}
{"level":"info","message":"✅ Services initialization completed","timestamp":"2025-06-20 18:02:57:257"}
{"level":"info","message":"🔐 Setting up session middleware...","timestamp":"2025-06-20 18:02:57:257"}
{"level":"info","message":"🛡️ Setting up rate limiting...","timestamp":"2025-06-20 18:02:57:257"}
{"level":"info","message":"📋 Setting up API routes...","timestamp":"2025-06-20 18:02:57:257"}
{"level":"info","message":"📧 Email service initialized (verification disabled for testing)","timestamp":"2025-06-20 18:02:57:257"}
{"level":"info","message":"Email templates initialized successfully","timestamp":"2025-06-20 18:02:58:258"}
{"level":"info","message":"✅ API routes configured","timestamp":"2025-06-20 18:02:58:258"}
{"level":"info","message":"💬 Initializing Socket.IO service...","timestamp":"2025-06-20 18:02:58:258"}
{"level":"info","message":"✅ Socket.IO service initialized","timestamp":"2025-06-20 18:02:58:258"}
{"level":"info","message":"🚀 LajoSpaces Backend Server running on port 5000","timestamp":"2025-06-20 18:02:58:258"}
{"level":"info","message":"🌍 Environment: development","timestamp":"2025-06-20 18:02:58:258"}
{"level":"info","message":"📊 Health check: http://localhost:5000/health","timestamp":"2025-06-20 18:02:58:258"}
{"level":"info","message":"🔗 API Base URL: http://localhost:5000/api","timestamp":"2025-06-20 18:02:58:258"}
{"level":"info","message":"💬 Socket.IO enabled for real-time messaging","timestamp":"2025-06-20 18:02:58:258"}
{"error":{"message":"Route /api/auth/health not found on this server","name":"Error","stack":"Error: Route /api/auth/health not found on this server\n    at notFoundHandler (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\middleware\\notFoundHandler.ts:9:17)\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts:180:3\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)"},"level":"error","message":"Error 404: Route /api/auth/health not found on this server","request":{"ip":"::1","method":"GET","url":"/api/auth/health","userAgent":"curl/8.6.0"},"timestamp":"2025-06-20 18:03:45:345","user":null}
{"level":"info","message":"::1 - - [20/Jun/2025:17:03:45 +0000] \"GET /api/auth/health HTTP/1.1\" 404 344 \"-\" \"curl/8.6.0\"","timestamp":"2025-06-20 18:03:45:345"}
{"endpoint":"/api/auth/health","eventType":"data_viewed","ipAddress":"::1","level":"info","message":"Audit event logged","success":false,"timestamp":"2025-06-20 18:03:45:345"}
{"level":"info","message":"🚀 Starting server initialization...","timestamp":"2025-06-20 18:04:43:443"}
{"level":"info","message":"🚀 Starting LajoSpaces Backend Server...","timestamp":"2025-06-20 18:04:43:443"}
{"level":"info","message":"📊 Connecting to MongoDB...","timestamp":"2025-06-20 18:04:43:443"}
{"level":"info","message":"📊 Connecting to MongoDB...","timestamp":"2025-06-20 18:04:43:443"}
{"level":"info","message":"📊 Mongoose connected to MongoDB","timestamp":"2025-06-20 18:04:46:446"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-20 18:04:46:446"}
{"level":"info","message":"📊 Connected to database: lajospaces","timestamp":"2025-06-20 18:04:46:446"}
{"level":"info","message":"✅ MongoDB connection completed","timestamp":"2025-06-20 18:04:46:446"}
{"level":"info","message":"🔴 Connecting to Redis...","timestamp":"2025-06-20 18:04:46:446"}
{"level":"info","message":"🔴 Connecting to Redis...","timestamp":"2025-06-20 18:04:46:446"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:04:47:447"}
{"level":"info","message":"✅ Redis client ready","timestamp":"2025-06-20 18:04:47:447"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-20 18:04:47:447"}
{"level":"info","message":"✅ Redis connection completed","timestamp":"2025-06-20 18:04:47:447"}
{"level":"info","message":"⚙️ Initializing services...","timestamp":"2025-06-20 18:04:47:447"}
{"level":"info","message":"🔄 Connecting cache service...","timestamp":"2025-06-20 18:04:47:447"}
{"level":"info","message":"Redis cache client connecting...","timestamp":"2025-06-20 18:04:47:447"}
{"level":"info","message":"Redis cache client connected and ready","timestamp":"2025-06-20 18:04:48:448"}
{"level":"info","message":"✅ Cache service connected","timestamp":"2025-06-20 18:04:48:448"}
{"level":"info","message":"🔄 Connecting session service...","timestamp":"2025-06-20 18:04:48:448"}
{"level":"info","message":"Redis session client connecting...","timestamp":"2025-06-20 18:04:48:448"}
{"level":"info","message":"Redis session client connected and ready","timestamp":"2025-06-20 18:04:48:448"}
{"level":"info","message":"✅ Session service connected","timestamp":"2025-06-20 18:04:48:448"}
{"level":"info","message":"🔄 Connecting token service...","timestamp":"2025-06-20 18:04:48:448"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:04:48:448"}
{"level":"info","message":"Redis token client connected and ready","timestamp":"2025-06-20 18:04:48:448"}
{"level":"info","message":"✅ Token service connected","timestamp":"2025-06-20 18:04:48:448"}
{"level":"info","message":"All services initialized successfully","timestamp":"2025-06-20 18:04:48:448"}
{"level":"info","message":"✅ Services initialization completed","timestamp":"2025-06-20 18:04:48:448"}
{"level":"info","message":"🔐 Setting up session middleware...","timestamp":"2025-06-20 18:04:48:448"}
{"level":"info","message":"🛡️ Setting up rate limiting...","timestamp":"2025-06-20 18:04:48:448"}
{"level":"info","message":"📋 Setting up API routes...","timestamp":"2025-06-20 18:04:48:448"}
{"level":"info","message":"📧 Email service initialized (verification disabled for testing)","timestamp":"2025-06-20 18:04:49:449"}
{"level":"info","message":"Email templates initialized successfully","timestamp":"2025-06-20 18:04:49:449"}
{"level":"info","message":"✅ API routes configured","timestamp":"2025-06-20 18:04:50:450"}
{"level":"info","message":"💬 Initializing Socket.IO service...","timestamp":"2025-06-20 18:04:50:450"}
{"level":"info","message":"✅ Socket.IO service initialized","timestamp":"2025-06-20 18:04:50:450"}
{"level":"info","message":"🚀 LajoSpaces Backend Server running on port 5000","timestamp":"2025-06-20 18:04:50:450"}
{"level":"info","message":"🌍 Environment: development","timestamp":"2025-06-20 18:04:50:450"}
{"level":"info","message":"📊 Health check: http://localhost:5000/health","timestamp":"2025-06-20 18:04:50:450"}
{"level":"info","message":"🔗 API Base URL: http://localhost:5000/api","timestamp":"2025-06-20 18:04:50:450"}
{"level":"info","message":"💬 Socket.IO enabled for real-time messaging","timestamp":"2025-06-20 18:04:50:450"}
{"error":{"message":"Route /api/auth/health not found on this server","name":"Error","stack":"Error: Route /api/auth/health not found on this server\n    at notFoundHandler (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\middleware\\notFoundHandler.ts:9:17)\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts:180:3\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)"},"level":"error","message":"Error 404: Route /api/auth/health not found on this server","request":{"ip":"::1","method":"GET","url":"/api/auth/health","userAgent":"curl/8.6.0"},"timestamp":"2025-06-20 18:05:22:522","user":null}
{"level":"info","message":"::1 - - [20/Jun/2025:17:05:22 +0000] \"GET /api/auth/health HTTP/1.1\" 404 344 \"-\" \"curl/8.6.0\"","timestamp":"2025-06-20 18:05:22:522"}
{"endpoint":"/api/auth/health","eventType":"data_viewed","ipAddress":"::1","level":"info","message":"Audit event logged","success":false,"timestamp":"2025-06-20 18:05:22:522"}
{"level":"info","message":"🚀 Starting server initialization...","timestamp":"2025-06-20 18:06:00:60"}
{"level":"info","message":"🚀 Starting LajoSpaces Backend Server...","timestamp":"2025-06-20 18:06:00:60"}
{"level":"info","message":"📊 Connecting to MongoDB...","timestamp":"2025-06-20 18:06:00:60"}
{"level":"info","message":"📊 Connecting to MongoDB...","timestamp":"2025-06-20 18:06:00:60"}
{"level":"info","message":"📊 Mongoose connected to MongoDB","timestamp":"2025-06-20 18:06:01:61"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-20 18:06:01:61"}
{"level":"info","message":"📊 Connected to database: lajospaces","timestamp":"2025-06-20 18:06:01:61"}
{"level":"info","message":"✅ MongoDB connection completed","timestamp":"2025-06-20 18:06:01:61"}
{"level":"info","message":"🔴 Connecting to Redis...","timestamp":"2025-06-20 18:06:01:61"}
{"level":"info","message":"🔴 Connecting to Redis...","timestamp":"2025-06-20 18:06:01:61"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:06:02:62"}
{"level":"info","message":"✅ Redis client ready","timestamp":"2025-06-20 18:06:02:62"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-20 18:06:02:62"}
{"level":"info","message":"✅ Redis connection completed","timestamp":"2025-06-20 18:06:02:62"}
{"level":"info","message":"⚙️ Initializing services...","timestamp":"2025-06-20 18:06:02:62"}
{"level":"info","message":"🔄 Connecting cache service...","timestamp":"2025-06-20 18:06:02:62"}
{"level":"info","message":"Redis cache client connecting...","timestamp":"2025-06-20 18:06:02:62"}
{"level":"info","message":"Redis cache client connected and ready","timestamp":"2025-06-20 18:06:03:63"}
{"level":"info","message":"✅ Cache service connected","timestamp":"2025-06-20 18:06:03:63"}
{"level":"info","message":"🔄 Connecting session service...","timestamp":"2025-06-20 18:06:03:63"}
{"level":"info","message":"Redis session client connecting...","timestamp":"2025-06-20 18:06:03:63"}
{"level":"info","message":"Redis session client connected and ready","timestamp":"2025-06-20 18:06:03:63"}
{"level":"info","message":"✅ Session service connected","timestamp":"2025-06-20 18:06:03:63"}
{"level":"info","message":"🔄 Connecting token service...","timestamp":"2025-06-20 18:06:03:63"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:06:03:63"}
{"level":"info","message":"Redis token client connected and ready","timestamp":"2025-06-20 18:06:03:63"}
{"level":"info","message":"✅ Token service connected","timestamp":"2025-06-20 18:06:03:63"}
{"level":"info","message":"All services initialized successfully","timestamp":"2025-06-20 18:06:03:63"}
{"level":"info","message":"✅ Services initialization completed","timestamp":"2025-06-20 18:06:03:63"}
{"level":"info","message":"🔐 Setting up session middleware...","timestamp":"2025-06-20 18:06:03:63"}
{"level":"info","message":"🛡️ Setting up rate limiting...","timestamp":"2025-06-20 18:06:03:63"}
{"level":"info","message":"📋 Setting up API routes...","timestamp":"2025-06-20 18:06:03:63"}
{"level":"info","message":"📧 Email service initialized (verification disabled for testing)","timestamp":"2025-06-20 18:06:04:64"}
{"level":"info","message":"Email templates initialized successfully","timestamp":"2025-06-20 18:06:05:65"}
{"level":"info","message":"🔗 Mounting auth routes...","timestamp":"2025-06-20 18:06:05:65"}
{"level":"info","message":"🔗 Mounting user routes...","timestamp":"2025-06-20 18:06:05:65"}
{"level":"info","message":"🔗 Mounting profile routes...","timestamp":"2025-06-20 18:06:05:65"}
{"level":"info","message":"🔗 Mounting photo routes...","timestamp":"2025-06-20 18:06:05:65"}
{"level":"info","message":"🔗 Mounting upload routes...","timestamp":"2025-06-20 18:06:05:65"}
{"level":"info","message":"🔗 Mounting search routes...","timestamp":"2025-06-20 18:06:05:65"}
{"level":"info","message":"🔗 Mounting property routes...","timestamp":"2025-06-20 18:06:05:65"}
{"level":"info","message":"🔗 All routes mounted successfully","timestamp":"2025-06-20 18:06:05:65"}
{"level":"info","message":"✅ API routes configured","timestamp":"2025-06-20 18:06:05:65"}
{"level":"info","message":"💬 Initializing Socket.IO service...","timestamp":"2025-06-20 18:06:05:65"}
{"level":"info","message":"✅ Socket.IO service initialized","timestamp":"2025-06-20 18:06:05:65"}
{"level":"info","message":"🚀 LajoSpaces Backend Server running on port 5000","timestamp":"2025-06-20 18:06:05:65"}
{"level":"info","message":"🌍 Environment: development","timestamp":"2025-06-20 18:06:05:65"}
{"level":"info","message":"📊 Health check: http://localhost:5000/health","timestamp":"2025-06-20 18:06:05:65"}
{"level":"info","message":"🔗 API Base URL: http://localhost:5000/api","timestamp":"2025-06-20 18:06:05:65"}
{"level":"info","message":"💬 Socket.IO enabled for real-time messaging","timestamp":"2025-06-20 18:06:05:65"}
{"error":{"message":"Route /api/auth/health not found on this server","name":"Error","stack":"Error: Route /api/auth/health not found on this server\n    at notFoundHandler (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\middleware\\notFoundHandler.ts:9:17)\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts:188:3\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)"},"level":"error","message":"Error 404: Route /api/auth/health not found on this server","request":{"ip":"::1","method":"GET","url":"/api/auth/health","userAgent":"curl/8.6.0"},"timestamp":"2025-06-20 18:06:40:640","user":null}
{"level":"info","message":"::1 - - [20/Jun/2025:17:06:40 +0000] \"GET /api/auth/health HTTP/1.1\" 404 344 \"-\" \"curl/8.6.0\"","timestamp":"2025-06-20 18:06:40:640"}
{"endpoint":"/api/auth/health","eventType":"data_viewed","ipAddress":"::1","level":"info","message":"Audit event logged","success":false,"timestamp":"2025-06-20 18:06:40:640"}
{"level":"info","message":"🚀 Starting server initialization...","timestamp":"2025-06-20 18:08:01:81"}
{"level":"info","message":"🚀 Starting LajoSpaces Backend Server...","timestamp":"2025-06-20 18:08:01:81"}
{"level":"info","message":"📊 Connecting to MongoDB...","timestamp":"2025-06-20 18:08:01:81"}
{"level":"info","message":"📊 Connecting to MongoDB...","timestamp":"2025-06-20 18:08:01:81"}
{"level":"info","message":"📊 Mongoose connected to MongoDB","timestamp":"2025-06-20 18:08:03:83"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-20 18:08:03:83"}
{"level":"info","message":"📊 Connected to database: lajospaces","timestamp":"2025-06-20 18:08:03:83"}
{"level":"info","message":"✅ MongoDB connection completed","timestamp":"2025-06-20 18:08:03:83"}
{"level":"info","message":"🔴 Connecting to Redis...","timestamp":"2025-06-20 18:08:03:83"}
{"level":"info","message":"🔴 Connecting to Redis...","timestamp":"2025-06-20 18:08:03:83"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:08:04:84"}
{"level":"info","message":"✅ Redis client ready","timestamp":"2025-06-20 18:08:04:84"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-20 18:08:04:84"}
{"level":"info","message":"✅ Redis connection completed","timestamp":"2025-06-20 18:08:04:84"}
{"level":"info","message":"⚙️ Initializing services...","timestamp":"2025-06-20 18:08:04:84"}
{"level":"info","message":"🔄 Connecting cache service...","timestamp":"2025-06-20 18:08:04:84"}
{"level":"info","message":"Redis cache client connecting...","timestamp":"2025-06-20 18:08:04:84"}
{"level":"info","message":"Redis cache client connected and ready","timestamp":"2025-06-20 18:08:04:84"}
{"level":"info","message":"✅ Cache service connected","timestamp":"2025-06-20 18:08:04:84"}
{"level":"info","message":"🔄 Connecting session service...","timestamp":"2025-06-20 18:08:04:84"}
{"level":"info","message":"Redis session client connecting...","timestamp":"2025-06-20 18:08:05:85"}
{"level":"info","message":"Redis session client connected and ready","timestamp":"2025-06-20 18:08:05:85"}
{"level":"info","message":"✅ Session service connected","timestamp":"2025-06-20 18:08:05:85"}
{"level":"info","message":"🔄 Connecting token service...","timestamp":"2025-06-20 18:08:05:85"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:08:05:85"}
{"level":"info","message":"Redis token client connected and ready","timestamp":"2025-06-20 18:08:05:85"}
{"level":"info","message":"✅ Token service connected","timestamp":"2025-06-20 18:08:05:85"}
{"level":"info","message":"All services initialized successfully","timestamp":"2025-06-20 18:08:05:85"}
{"level":"info","message":"✅ Services initialization completed","timestamp":"2025-06-20 18:08:05:85"}
{"level":"info","message":"🔐 Setting up session middleware...","timestamp":"2025-06-20 18:08:05:85"}
{"level":"info","message":"🛡️ Setting up rate limiting...","timestamp":"2025-06-20 18:08:05:85"}
{"level":"info","message":"📋 Setting up API routes...","timestamp":"2025-06-20 18:08:05:85"}
{"level":"info","message":"📧 Email service initialized (verification disabled for testing)","timestamp":"2025-06-20 18:08:06:86"}
{"level":"info","message":"Email templates initialized successfully","timestamp":"2025-06-20 18:08:06:86"}
{"level":"info","message":"🔗 Mounting auth routes...","timestamp":"2025-06-20 18:08:06:86"}
{"level":"info","message":"🔗 Mounting user routes...","timestamp":"2025-06-20 18:08:06:86"}
{"level":"info","message":"🔗 Mounting profile routes...","timestamp":"2025-06-20 18:08:06:86"}
{"level":"info","message":"🔗 Mounting photo routes...","timestamp":"2025-06-20 18:08:06:86"}
{"level":"info","message":"🔗 Mounting upload routes...","timestamp":"2025-06-20 18:08:06:86"}
{"level":"info","message":"🔗 Mounting search routes...","timestamp":"2025-06-20 18:08:06:86"}
{"level":"info","message":"🔗 Mounting property routes...","timestamp":"2025-06-20 18:08:06:86"}
{"level":"info","message":"🔗 All routes mounted successfully","timestamp":"2025-06-20 18:08:06:86"}
{"level":"info","message":"✅ API routes configured","timestamp":"2025-06-20 18:08:06:86"}
{"level":"info","message":"💬 Initializing Socket.IO service...","timestamp":"2025-06-20 18:08:06:86"}
{"level":"info","message":"✅ Socket.IO service initialized","timestamp":"2025-06-20 18:08:07:87"}
{"error":{"message":"Route /api/properties/health not found on this server","name":"Error","stack":"Error: Route /api/properties/health not found on this server\n    at notFoundHandler (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\middleware\\notFoundHandler.ts:9:17)\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts:188:3\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)"},"level":"error","message":"Error 404: Route /api/properties/health not found on this server","request":{"ip":"::1","method":"GET","url":"/api/properties/health","userAgent":"curl/8.6.0"},"timestamp":"2025-06-20 18:09:58:958","user":null}
{"level":"info","message":"::1 - - [20/Jun/2025:17:09:58 +0000] \"GET /api/properties/health HTTP/1.1\" 404 362 \"-\" \"curl/8.6.0\"","timestamp":"2025-06-20 18:09:58:958"}
{"endpoint":"/api/properties/health","eventType":"data_viewed","ipAddress":"::1","level":"info","message":"Audit event logged","success":false,"timestamp":"2025-06-20 18:09:58:958"}
{"error":{"message":"Route /api/properties/health not found on this server","name":"Error","stack":"Error: Route /api/properties/health not found on this server\n    at notFoundHandler (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\middleware\\notFoundHandler.ts:9:17)\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts:188:3\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)"},"level":"error","message":"Error 404: Route /api/properties/health not found on this server","request":{"ip":"::1","method":"GET","url":"/api/properties/health","userAgent":"curl/8.6.0"},"timestamp":"2025-06-20 18:10:12:1012","user":null}
{"level":"info","message":"::1 - - [20/Jun/2025:17:10:12 +0000] \"GET /api/properties/health HTTP/1.1\" 404 362 \"-\" \"curl/8.6.0\"","timestamp":"2025-06-20 18:10:12:1012"}
{"endpoint":"/api/properties/health","eventType":"data_viewed","ipAddress":"::1","level":"info","message":"Audit event logged","success":false,"timestamp":"2025-06-20 18:10:12:1012"}
{"error":{"message":"Route /api/properties/health not found on this server","name":"Error","stack":"Error: Route /api/properties/health not found on this server\n    at notFoundHandler (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\middleware\\notFoundHandler.ts:9:17)\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts:188:3\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)"},"level":"error","message":"Error 404: Route /api/properties/health not found on this server","request":{"ip":"::1","method":"GET","url":"/api/properties/health","userAgent":"curl/8.6.0"},"timestamp":"2025-06-20 18:13:39:1339","user":null}
{"level":"info","message":"::1 - - [20/Jun/2025:17:13:39 +0000] \"GET /api/properties/health HTTP/1.1\" 404 362 \"-\" \"curl/8.6.0\"","timestamp":"2025-06-20 18:13:39:1339"}
{"endpoint":"/api/properties/health","eventType":"data_viewed","ipAddress":"::1","level":"info","message":"Audit event logged","success":false,"timestamp":"2025-06-20 18:13:39:1339"}
{"error":{"message":"Route /api/properties?limit=1 not found on this server","name":"Error","stack":"Error: Route /api/properties?limit=1 not found on this server\n    at notFoundHandler (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\middleware\\notFoundHandler.ts:9:17)\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts:188:3\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)"},"level":"error","message":"Error 404: Route /api/properties?limit=1 not found on this server","request":{"ip":"::1","method":"GET","url":"/api/properties?limit=1","userAgent":"curl/8.6.0"},"timestamp":"2025-06-20 18:13:58:1358","user":null}
{"level":"info","message":"::1 - - [20/Jun/2025:17:13:58 +0000] \"GET /api/properties?limit=1 HTTP/1.1\" 404 357 \"-\" \"curl/8.6.0\"","timestamp":"2025-06-20 18:13:58:1358"}
{"endpoint":"/api/properties?limit=1","eventType":"data_viewed","ipAddress":"::1","level":"info","message":"Audit event logged","success":false,"timestamp":"2025-06-20 18:13:58:1358"}
{"error":{"message":"Route /api/properties?limit=1 not found on this server","name":"Error","stack":"Error: Route /api/properties?limit=1 not found on this server\n    at notFoundHandler (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\middleware\\notFoundHandler.ts:9:17)\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts:188:3\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)"},"level":"error","message":"Error 404: Route /api/properties?limit=1 not found on this server","request":{"ip":"::1","method":"GET","url":"/api/properties?limit=1","userAgent":"curl/8.6.0"},"timestamp":"2025-06-20 18:14:41:1441","user":null}
{"level":"info","message":"::1 - - [20/Jun/2025:17:14:41 +0000] \"GET /api/properties?limit=1 HTTP/1.1\" 404 357 \"-\" \"curl/8.6.0\"","timestamp":"2025-06-20 18:14:41:1441"}
{"endpoint":"/api/properties?limit=1","eventType":"data_viewed","ipAddress":"::1","level":"info","message":"Audit event logged","success":false,"timestamp":"2025-06-20 18:14:41:1441"}
