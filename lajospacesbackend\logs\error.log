{"code":"ESOCKET","command":"CONN","level":"error","library":"SSL routines","message":"Email transporter configuration error: 084A0000:error:0A00010B:SSL routines:ssl3_get_record:wrong version number:c:\\ws\\deps\\openssl\\openssl\\ssl\\record\\ssl3_record.c:355:\n","reason":"wrong version number","stack":"Error: 084A0000:error:0A00010B:SSL routines:ssl3_get_record:wrong version number:c:\\ws\\deps\\openssl\\openssl\\ssl\\record\\ssl3_record.c:355:\n","timestamp":"2025-06-20 13:09:34:934"}
{"code":"ESOCKET","command":"CONN","level":"error","library":"SSL routines","message":"Email transporter configuration error: E0550000:error:0A00010B:SSL routines:ssl3_get_record:wrong version number:c:\\ws\\deps\\openssl\\openssl\\ssl\\record\\ssl3_record.c:355:\n","reason":"wrong version number","stack":"Error: E0550000:error:0A00010B:SSL routines:ssl3_get_record:wrong version number:c:\\ws\\deps\\openssl\\openssl\\ssl\\record\\ssl3_record.c:355:\n","timestamp":"2025-06-20 13:13:44:1344"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 14:57:40:5740"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 15:00:10:010"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 15:00:15:015"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 15:00:20:020"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 15:00:25:025"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 15:00:31:031"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 15:00:36:036"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 15:00:42:042"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 15:00:47:047"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 15:00:53:053"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 15:00:58:058"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 15:01:04:14"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 15:07:43:743"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 15:07:49:749"}
{"error":{"message":"Route /favicon.ico not found on this server","name":"Error","stack":"Error: Route /favicon.ico not found on this server\n    at notFoundHandler (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\middleware\\notFoundHandler.ts:9:17)\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at logger (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\morgan\\index.js:144:5)\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)"},"level":"error","message":"Error 404: Route /favicon.ico not found on this server","request":{"ip":"::1","method":"GET","url":"/favicon.ico","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"},"timestamp":"2025-06-20 15:12:12:1212","user":null}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 15:22:56:2256"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 15:23:02:232"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 15:23:07:237"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 15:23:12:2312"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 15:24:58:2458"}
{"error":{"message":"Route /api/properties/health not found on this server","name":"Error","stack":"Error: Route /api/properties/health not found on this server\n    at notFoundHandler (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\middleware\\notFoundHandler.ts:9:17)\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at logger (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\morgan\\index.js:144:5)\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)"},"level":"error","message":"Error 404: Route /api/properties/health not found on this server","request":{"ip":"::1","method":"GET","url":"/api/properties/health","userAgent":"curl/8.6.0"},"timestamp":"2025-06-20 15:32:30:3230","user":null}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 15:32:57:3257"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 15:33:02:332"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 15:33:07:337"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 15:33:13:3313"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 15:33:18:3318"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 15:34:44:3444"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 15:40:20:4020"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 15:40:25:4025"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 15:40:31:4031"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 15:40:36:4036"}
{"cause":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{"ac-5ev0ybl-shard-00-00.5pikccg.mongodb.net:27017":{"$clusterTime":null,"address":"ac-5ev0ybl-shard-00-00.5pikccg.mongodb.net:27017","arbiters":[],"electionId":null,"error":null,"hosts":[],"iscryptd":false,"lastUpdateTime":13530346,"lastWriteDate":0,"logicalSessionTimeoutMinutes":null,"maxBsonObjectSize":null,"maxMessageSizeBytes":null,"maxWireVersion":0,"maxWriteBatchSize":null,"me":null,"minRoundTripTime":0,"minWireVersion":0,"passives":[],"primary":null,"roundTripTime":-1,"setName":null,"setVersion":null,"tags":{},"topologyVersion":null,"type":"Unknown"},"ac-5ev0ybl-shard-00-01.5pikccg.mongodb.net:27017":{"$clusterTime":null,"address":"ac-5ev0ybl-shard-00-01.5pikccg.mongodb.net:27017","arbiters":[],"electionId":null,"error":null,"hosts":[],"iscryptd":false,"lastUpdateTime":13530346,"lastWriteDate":0,"logicalSessionTimeoutMinutes":null,"maxBsonObjectSize":null,"maxMessageSizeBytes":null,"maxWireVersion":0,"maxWriteBatchSize":null,"me":null,"minRoundTripTime":0,"minWireVersion":0,"passives":[],"primary":null,"roundTripTime":-1,"setName":null,"setVersion":null,"tags":{},"topologyVersion":null,"type":"Unknown"},"ac-5ev0ybl-shard-00-02.5pikccg.mongodb.net:27017":{"$clusterTime":null,"address":"ac-5ev0ybl-shard-00-02.5pikccg.mongodb.net:27017","arbiters":[],"electionId":null,"error":null,"hosts":[],"iscryptd":false,"lastUpdateTime":13530346,"lastWriteDate":0,"logicalSessionTimeoutMinutes":null,"maxBsonObjectSize":null,"maxMessageSizeBytes":null,"maxWireVersion":0,"maxWriteBatchSize":null,"me":null,"minRoundTripTime":0,"minWireVersion":0,"passives":[],"primary":null,"roundTripTime":-1,"setName":null,"setVersion":null,"tags":{},"topologyVersion":null,"type":"Unknown"}},"setName":"atlas-be77u7-shard-0","stale":false,"type":"ReplicaSetNoPrimary"},"errorLabelSet":{},"level":"error","message":"❌ MongoDB connection failed: Could not connect to any servers in your MongoDB Atlas cluster. One common reason is that you're trying to access the database from an IP that isn't whitelisted. Make sure your current IP address is on your Atlas cluster's IP whitelist: https://www.mongodb.com/docs/atlas/security-whitelist/","reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{"ac-5ev0ybl-shard-00-00.5pikccg.mongodb.net:27017":{"$clusterTime":null,"address":"ac-5ev0ybl-shard-00-00.5pikccg.mongodb.net:27017","arbiters":[],"electionId":null,"error":null,"hosts":[],"iscryptd":false,"lastUpdateTime":13530346,"lastWriteDate":0,"logicalSessionTimeoutMinutes":null,"maxBsonObjectSize":null,"maxMessageSizeBytes":null,"maxWireVersion":0,"maxWriteBatchSize":null,"me":null,"minRoundTripTime":0,"minWireVersion":0,"passives":[],"primary":null,"roundTripTime":-1,"setName":null,"setVersion":null,"tags":{},"topologyVersion":null,"type":"Unknown"},"ac-5ev0ybl-shard-00-01.5pikccg.mongodb.net:27017":{"$clusterTime":null,"address":"ac-5ev0ybl-shard-00-01.5pikccg.mongodb.net:27017","arbiters":[],"electionId":null,"error":null,"hosts":[],"iscryptd":false,"lastUpdateTime":13530346,"lastWriteDate":0,"logicalSessionTimeoutMinutes":null,"maxBsonObjectSize":null,"maxMessageSizeBytes":null,"maxWireVersion":0,"maxWriteBatchSize":null,"me":null,"minRoundTripTime":0,"minWireVersion":0,"passives":[],"primary":null,"roundTripTime":-1,"setName":null,"setVersion":null,"tags":{},"topologyVersion":null,"type":"Unknown"},"ac-5ev0ybl-shard-00-02.5pikccg.mongodb.net:27017":{"$clusterTime":null,"address":"ac-5ev0ybl-shard-00-02.5pikccg.mongodb.net:27017","arbiters":[],"electionId":null,"error":null,"hosts":[],"iscryptd":false,"lastUpdateTime":13530346,"lastWriteDate":0,"logicalSessionTimeoutMinutes":null,"maxBsonObjectSize":null,"maxMessageSizeBytes":null,"maxWireVersion":0,"maxWriteBatchSize":null,"me":null,"minRoundTripTime":0,"minWireVersion":0,"passives":[],"primary":null,"roundTripTime":-1,"setName":null,"setVersion":null,"tags":{},"topologyVersion":null,"type":"Unknown"}},"setName":"atlas-be77u7-shard-0","stale":false,"type":"ReplicaSetNoPrimary"},"stack":"MongooseServerSelectionError: Could not connect to any servers in your MongoDB Atlas cluster. One common reason is that you're trying to access the database from an IP that isn't whitelisted. Make sure your current IP address is on your Atlas cluster's IP whitelist: https://www.mongodb.com/docs/atlas/security-whitelist/\n    at _handleConnectionErrors (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\mongoose\\lib\\connection.js:1165:11)\n    at NativeConnection.openUri (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\mongoose\\lib\\connection.js:1096:11)\n    at async connectDatabase (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\config\\database.ts:29:5)\n    at async startServer (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts:214:5)","timestamp":"2025-06-20 16:24:14:2414"}
{"code":"MODULE_NOT_FOUND","level":"error","message":"Failed to setup routes: Cannot find module 'sharp'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts","requireStack":["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts"],"stack":"Error: Cannot find module 'sharp'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1144:15)\n    at Function.Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js:811:30)\n    at Function.Module._load (node:internal/modules/cjs/loader:985:27)\n    at Module.require (node:internal/modules/cjs/loader:1235:19)\n    at require (node:internal/modules/helpers:176:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts:1:1)\n    at Module._compile (node:internal/modules/cjs/loader:1376:14)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1621:12)","timestamp":"2025-06-20 17:56:30:5630"}
{"code":"MODULE_NOT_FOUND","level":"error","message":"Failed to setup routes: Cannot find module '../middleware/authMiddleware'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\admin.routes.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts","requireStack":["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\admin.routes.ts","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts"],"stack":"Error: Cannot find module '../middleware/authMiddleware'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\admin.routes.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1144:15)\n    at Function.Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js:811:30)\n    at Function.Module._load (node:internal/modules/cjs/loader:985:27)\n    at Module.require (node:internal/modules/cjs/loader:1235:19)\n    at require (node:internal/modules/helpers:176:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\admin.routes.ts:2:1)\n    at Module._compile (node:internal/modules/cjs/loader:1376:14)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1621:12)","timestamp":"2025-06-20 17:57:26:5726"}
{"error":{"message":"Route /api/auth/health not found on this server","name":"Error","stack":"Error: Route /api/auth/health not found on this server\n    at notFoundHandler (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\middleware\\notFoundHandler.ts:9:17)\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts:180:3\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)"},"level":"error","message":"Error 404: Route /api/auth/health not found on this server","request":{"ip":"::1","method":"GET","url":"/api/auth/health","userAgent":"curl/8.6.0"},"timestamp":"2025-06-20 18:00:37:037","user":null}
{"error":{"message":"Route /api/auth/health not found on this server","name":"Error","stack":"Error: Route /api/auth/health not found on this server\n    at notFoundHandler (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\middleware\\notFoundHandler.ts:9:17)\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts:180:3\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)"},"level":"error","message":"Error 404: Route /api/auth/health not found on this server","request":{"ip":"::1","method":"GET","url":"/api/auth/health","userAgent":"curl/8.6.0"},"timestamp":"2025-06-20 18:00:51:051","user":null}
{"error":{"message":"Route /api/auth/health not found on this server","name":"Error","stack":"Error: Route /api/auth/health not found on this server\n    at notFoundHandler (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\middleware\\notFoundHandler.ts:9:17)\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts:180:3\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)"},"level":"error","message":"Error 404: Route /api/auth/health not found on this server","request":{"ip":"::1","method":"GET","url":"/api/auth/health","userAgent":"curl/8.6.0"},"timestamp":"2025-06-20 18:01:08:18","user":null}
{"error":{"message":"Route /api/users/health not found on this server","name":"Error","stack":"Error: Route /api/users/health not found on this server\n    at notFoundHandler (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\middleware\\notFoundHandler.ts:9:17)\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts:180:3\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)"},"level":"error","message":"Error 404: Route /api/users/health not found on this server","request":{"ip":"::1","method":"GET","url":"/api/users/health","userAgent":"curl/8.6.0"},"timestamp":"2025-06-20 18:01:28:128","user":null}
{"error":{"message":"Route /api/auth/health not found on this server","name":"Error","stack":"Error: Route /api/auth/health not found on this server\n    at notFoundHandler (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\middleware\\notFoundHandler.ts:9:17)\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts:180:3\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)"},"level":"error","message":"Error 404: Route /api/auth/health not found on this server","request":{"ip":"::1","method":"GET","url":"/api/auth/health","userAgent":"curl/8.6.0"},"timestamp":"2025-06-20 18:03:45:345","user":null}
{"error":{"message":"Route /api/auth/health not found on this server","name":"Error","stack":"Error: Route /api/auth/health not found on this server\n    at notFoundHandler (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\middleware\\notFoundHandler.ts:9:17)\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts:180:3\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)"},"level":"error","message":"Error 404: Route /api/auth/health not found on this server","request":{"ip":"::1","method":"GET","url":"/api/auth/health","userAgent":"curl/8.6.0"},"timestamp":"2025-06-20 18:05:22:522","user":null}
{"error":{"message":"Route /api/auth/health not found on this server","name":"Error","stack":"Error: Route /api/auth/health not found on this server\n    at notFoundHandler (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\middleware\\notFoundHandler.ts:9:17)\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts:188:3\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)"},"level":"error","message":"Error 404: Route /api/auth/health not found on this server","request":{"ip":"::1","method":"GET","url":"/api/auth/health","userAgent":"curl/8.6.0"},"timestamp":"2025-06-20 18:06:40:640","user":null}
{"error":{"message":"Route /api/properties/health not found on this server","name":"Error","stack":"Error: Route /api/properties/health not found on this server\n    at notFoundHandler (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\middleware\\notFoundHandler.ts:9:17)\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts:188:3\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)"},"level":"error","message":"Error 404: Route /api/properties/health not found on this server","request":{"ip":"::1","method":"GET","url":"/api/properties/health","userAgent":"curl/8.6.0"},"timestamp":"2025-06-20 18:09:58:958","user":null}
{"error":{"message":"Route /api/properties/health not found on this server","name":"Error","stack":"Error: Route /api/properties/health not found on this server\n    at notFoundHandler (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\middleware\\notFoundHandler.ts:9:17)\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts:188:3\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)"},"level":"error","message":"Error 404: Route /api/properties/health not found on this server","request":{"ip":"::1","method":"GET","url":"/api/properties/health","userAgent":"curl/8.6.0"},"timestamp":"2025-06-20 18:10:12:1012","user":null}
{"error":{"message":"Route /api/properties/health not found on this server","name":"Error","stack":"Error: Route /api/properties/health not found on this server\n    at notFoundHandler (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\middleware\\notFoundHandler.ts:9:17)\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts:188:3\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)"},"level":"error","message":"Error 404: Route /api/properties/health not found on this server","request":{"ip":"::1","method":"GET","url":"/api/properties/health","userAgent":"curl/8.6.0"},"timestamp":"2025-06-20 18:13:39:1339","user":null}
{"error":{"message":"Route /api/properties?limit=1 not found on this server","name":"Error","stack":"Error: Route /api/properties?limit=1 not found on this server\n    at notFoundHandler (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\middleware\\notFoundHandler.ts:9:17)\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts:188:3\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)"},"level":"error","message":"Error 404: Route /api/properties?limit=1 not found on this server","request":{"ip":"::1","method":"GET","url":"/api/properties?limit=1","userAgent":"curl/8.6.0"},"timestamp":"2025-06-20 18:13:58:1358","user":null}
{"error":{"message":"Route /api/properties?limit=1 not found on this server","name":"Error","stack":"Error: Route /api/properties?limit=1 not found on this server\n    at notFoundHandler (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\middleware\\notFoundHandler.ts:9:17)\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts:188:3\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)"},"level":"error","message":"Error 404: Route /api/properties?limit=1 not found on this server","request":{"ip":"::1","method":"GET","url":"/api/properties?limit=1","userAgent":"curl/8.6.0"},"timestamp":"2025-06-20 18:14:41:1441","user":null}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis session client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:38:21:3821"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis cache client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:53:12:5312"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis session client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:53:12:5312"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis token client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:53:12:5312"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis session client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:53:12:5312"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis token client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:53:13:5313"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis token client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:53:14:5314"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:53:14:5314"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:53:15:5315"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:53:16:5316"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis session client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:53:16:5316"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis cache client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:53:17:5317"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis cache client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:53:17:5317"}
{"level":"error","message":"Redis cache client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:53:18:5318"}
{"level":"error","message":"Redis session client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:53:18:5318"}
{"level":"error","message":"Redis token client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:53:18:5318"}
{"level":"error","message":"Redis session client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:53:18:5318"}
{"level":"error","message":"Redis token client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:53:18:5318"}
{"level":"error","message":"Redis token client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:53:19:5319"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:53:19:5319"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:53:20:5320"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:53:21:5321"}
{"level":"error","message":"Redis session client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:53:21:5321"}
{"level":"error","message":"Redis cache client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:53:22:5322"}
{"level":"error","message":"Redis cache client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:53:22:5322"}
{"level":"error","message":"Redis cache client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:53:23:5323"}
{"level":"error","message":"Redis token client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:53:23:5323"}
{"level":"error","message":"Redis session client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:53:23:5323"}
{"level":"error","message":"Redis token client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:53:23:5323"}
{"level":"error","message":"Redis session client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:53:23:5323"}
{"level":"error","message":"Redis token client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:53:24:5324"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:53:24:5324"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:53:25:5325"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:53:26:5326"}
{"level":"error","message":"Redis session client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:53:26:5326"}
{"level":"error","message":"Redis cache client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:53:27:5327"}
{"level":"error","message":"Redis cache client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:53:27:5327"}
{"level":"error","message":"Redis token client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:53:28:5328"}
{"level":"error","message":"Redis session client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:53:28:5328"}
{"level":"error","message":"Redis token client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:53:28:5328"}
{"level":"error","message":"Redis session client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:53:28:5328"}
{"level":"error","message":"Redis cache client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:53:28:5328"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:53:29:5329"}
{"level":"error","message":"Redis token client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:53:29:5329"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:53:30:5330"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:53:31:5331"}
{"level":"error","message":"Redis session client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:53:31:5331"}
{"level":"error","message":"Redis cache client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:53:32:5332"}
{"level":"error","message":"Redis cache client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:53:32:5332"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:57:35:5735"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis token client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:57:35:5735"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis cache client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:57:35:5735"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis session client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:57:36:5736"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:57:36:5736"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis token client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:57:36:5736"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis session client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:57:36:5736"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis cache client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:57:36:5736"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis token client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:57:36:5736"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis cache client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:57:36:5736"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis session client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:57:36:5736"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:57:36:5736"}
{"level":"error","message":"Redis session client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:57:41:5741"}
{"level":"error","message":"Redis session client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:57:41:5741"}
{"level":"error","message":"Redis cache client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:57:41:5741"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:57:41:5741"}
{"level":"error","message":"Redis token client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:57:41:5741"}
{"level":"error","message":"Redis cache client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:57:41:5741"}
{"level":"error","message":"Redis cache client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:57:41:5741"}
{"level":"error","message":"Redis token client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:57:41:5741"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:57:41:5741"}
{"level":"error","message":"Redis session client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:57:41:5741"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:57:41:5741"}
{"level":"error","message":"Redis token client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:57:41:5741"}
{"level":"error","message":"Redis session client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:57:46:5746"}
{"level":"error","message":"Redis cache client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:57:46:5746"}
{"level":"error","message":"Redis session client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:57:46:5746"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:57:46:5746"}
{"level":"error","message":"Redis token client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:57:46:5746"}
{"level":"error","message":"Redis cache client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:57:46:5746"}
{"level":"error","message":"Redis cache client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:57:46:5746"}
{"level":"error","message":"Redis token client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:57:46:5746"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:57:46:5746"}
{"level":"error","message":"Redis session client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:57:46:5746"}
{"level":"error","message":"Redis token client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:57:46:5746"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:57:46:5746"}
{"level":"error","message":"Redis session client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:57:51:5751"}
{"level":"error","message":"Redis session client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:57:51:5751"}
{"level":"error","message":"Redis session client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:57:51:5751"}
{"level":"error","message":"Redis token client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:57:51:5751"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:57:51:5751"}
{"level":"error","message":"Redis cache client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:57:51:5751"}
{"level":"error","message":"Redis token client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:57:51:5751"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:57:51:5751"}
{"level":"error","message":"Redis cache client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:57:51:5751"}
{"level":"error","message":"Redis token client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:57:51:5751"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:57:51:5751"}
{"level":"error","message":"Redis cache client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:57:51:5751"}
{"level":"error","message":"Redis token client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:57:56:5756"}
{"level":"error","message":"Redis session client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:57:56:5756"}
{"level":"error","message":"Redis token client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:57:56:5756"}
{"level":"error","message":"Redis session client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:57:56:5756"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:57:56:5756"}
{"level":"error","message":"Redis cache client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:57:56:5756"}
{"level":"error","message":"Redis token client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:57:56:5756"}
{"level":"error","message":"Redis session client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:57:56:5756"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:57:56:5756"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:57:56:5756"}
{"level":"error","message":"Redis cache client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:57:56:5756"}
{"level":"error","message":"Redis cache client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:57:56:5756"}
{"level":"error","message":"Redis session client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:58:02:582"}
{"level":"error","message":"Redis session client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:58:02:582"}
{"level":"error","message":"Redis token client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:58:02:582"}
{"level":"error","message":"Redis session client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:58:02:582"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:58:02:582"}
{"level":"error","message":"Redis token client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:58:02:582"}
{"level":"error","message":"Redis token client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:58:02:582"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:58:02:582"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:58:02:582"}
{"level":"error","message":"Redis cache client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:58:02:582"}
{"level":"error","message":"Redis cache client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:58:02:582"}
{"level":"error","message":"Redis cache client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:58:02:582"}
{"level":"error","message":"Redis session client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:58:07:587"}
{"level":"error","message":"Redis session client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:58:07:587"}
{"level":"error","message":"Redis token client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:58:07:587"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:58:07:587"}
{"level":"error","message":"Redis token client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:58:07:587"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:58:07:587"}
{"level":"error","message":"Redis session client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:58:07:587"}
{"level":"error","message":"Redis token client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:58:07:587"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:58:07:587"}
{"level":"error","message":"Redis cache client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:58:07:587"}
{"level":"error","message":"Redis cache client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:58:07:587"}
{"level":"error","message":"Redis cache client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:58:07:587"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:58:12:5812"}
{"level":"error","message":"Redis token client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:58:12:5812"}
{"level":"error","message":"Redis session client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:58:12:5812"}
{"level":"error","message":"Redis session client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:58:12:5812"}
{"level":"error","message":"Redis token client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:58:12:5812"}
{"level":"error","message":"Redis token client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:58:12:5812"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:58:12:5812"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:58:12:5812"}
{"level":"error","message":"Redis session client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:58:12:5812"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:13:5813"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:13:5813"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:13:5813"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:13:5813"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:13:5813"}
{"level":"error","message":"Redis session client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:13:5813"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:13:5813"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:14:5814"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:14:5814"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:14:5814"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:14:5814"}
{"level":"error","message":"Redis session client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:14:5814"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:14:5814"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:15:5815"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:15:5815"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:15:5815"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:15:5815"}
{"level":"error","message":"Redis session client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:15:5815"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:15:5815"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:16:5816"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:16:5816"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:17:5817"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:17:5817"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:17:5817"}
{"level":"error","message":"Redis session client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:17:5817"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:17:5817"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:17:5817"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:17:5817"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:18:5818"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:18:5818"}
{"level":"error","message":"Redis session client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:18:5818"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:18:5818"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis token client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:18:5818"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis token client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:18:5818"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:19:5819"}
{"level":"error","message":"Redis session client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:19:5819"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:19:5819"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:19:5819"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:19:5819"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:19:5819"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:19:5819"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:19:5819"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:19:5819"}
{"level":"error","message":"Redis session client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:19:5819"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:20:5820"}
{"level":"error","message":"Redis session client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:20:5820"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:20:5820"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:20:5820"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:21:5821"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis token client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:21:5821"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis token client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:21:5821"}
{"level":"error","message":"Redis session client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:21:5821"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:21:5821"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:21:5821"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:21:5821"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:22:5822"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis token client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:22:5822"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis token client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:22:5822"}
{"level":"error","message":"Redis session client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:22:5822"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:22:5822"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:22:5822"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:23:5823"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:23:5823"}
{"level":"error","message":"Redis session client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:23:5823"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:23:5823"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:23:5823"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:23:5823"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:23:5823"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:24:5824"}
{"level":"error","message":"Redis session client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:24:5824"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:24:5824"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:24:5824"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis token client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:25:5825"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis token client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:25:5825"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:25:5825"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:25:5825"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:25:5825"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:25:5825"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:25:5825"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:25:5825"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis session client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:25:5825"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis session client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:25:5825"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:25:5825"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:25:5825"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:26:5826"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:26:5826"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:26:5826"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:27:5827"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:27:5827"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:27:5827"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:27:5827"}
{"level":"error","message":"Redis session client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:27:5827"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:28:5828"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:28:5828"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis token client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:28:5828"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis token client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:28:5828"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:28:5828"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:28:5828"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:28:5828"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:28:5828"}
{"level":"error","message":"Redis session client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:28:5828"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:29:5829"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:29:5829"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis token client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:29:5829"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis token client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:29:5829"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:29:5829"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:29:5829"}
{"level":"error","message":"Redis session client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:29:5829"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis token client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:29:5829"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis token client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:29:5829"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:30:5830"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:30:5830"}
{"level":"error","message":"Redis session client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:30:5830"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:31:5831"}
{"level":"error","message":"Redis session client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:31:5831"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:31:5831"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:31:5831"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:31:5831"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:31:5831"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:31:5831"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis session client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:32:5832"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:32:5832"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis session client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:32:5832"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:32:5832"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:32:5832"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:32:5832"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:33:5833"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:33:5833"}
{"level":"error","message":"Redis session client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:33:5833"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:33:5833"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:33:5833"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:33:5833"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:34:5834"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:34:5834"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis token client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:34:5834"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis token client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:34:5834"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:34:5834"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:34:5834"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis session client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:34:5834"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis session client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:34:5834"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:35:5835"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:35:5835"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:35:5835"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:35:5835"}
{"level":"error","message":"Redis session client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:35:5835"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:36:5836"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:36:5836"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:36:5836"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:36:5836"}
{"level":"error","message":"Redis session client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:36:5836"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:36:5836"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:37:5837"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:37:5837"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:37:5837"}
{"level":"error","message":"Redis session client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:37:5837"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis token client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:37:5837"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis token client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:37:5837"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:37:5837"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:37:5837"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:37:5837"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:38:5838"}
{"level":"error","message":"Redis session client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:38:5838"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:38:5838"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:38:5838"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:38:5838"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:38:5838"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:39:5839"}
{"level":"error","message":"Redis session client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:39:5839"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:39:5839"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:39:5839"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:39:5839"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:39:5839"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:39:5839"}
{"level":"error","message":"Redis session client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:40:5840"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:40:5840"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:40:5840"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:40:5840"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:40:5840"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:40:5840"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:41:5841"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis session client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:41:5841"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis session client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:41:5841"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:41:5841"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis token client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:41:5841"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis token client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:41:5841"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:42:5842"}
{"level":"error","message":"Redis session client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:42:5842"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:42:5842"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:42:5842"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:42:5842"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:42:5842"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:43:5843"}
{"level":"error","message":"Redis session client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:43:5843"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:43:5843"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:43:5843"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:43:5843"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:43:5843"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:44:5844"}
{"level":"error","message":"Redis session client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:44:5844"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:44:5844"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:44:5844"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:44:5844"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:44:5844"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:45:5845"}
{"level":"error","message":"Redis session client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:45:5845"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:45:5845"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:45:5845"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:45:5845"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:45:5845"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:45:5845"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:45:5845"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:45:5845"}
{"level":"error","message":"Redis session client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:46:5846"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:46:5846"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:46:5846"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:46:5846"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:46:5846"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:46:5846"}
{"level":"error","message":"Redis session client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:46:5846"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:47:5847"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:47:5847"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:47:5847"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:47:5847"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:47:5847"}
{"level":"error","message":"Redis session client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:47:5847"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:47:5847"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:48:5848"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:48:5848"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:48:5848"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:48:5848"}
{"level":"error","message":"Redis session client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:48:5848"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:48:5848"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:49:5849"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:49:5849"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:49:5849"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:49:5849"}
{"level":"error","message":"Redis session client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:49:5849"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:49:5849"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:50:5850"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:50:5850"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:50:5850"}
{"level":"error","message":"Redis session client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:50:5850"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:50:5850"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis token client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:50:5850"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis token client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:50:5850"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:51:5851"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:51:5851"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:51:5851"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:51:5851"}
{"level":"error","message":"Redis session client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:51:5851"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:51:5851"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:51:5851"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:51:5851"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:51:5851"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:52:5852"}
{"level":"error","message":"Redis session client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:52:5852"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:52:5852"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:52:5852"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:52:5852"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:53:5853"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis token client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:53:5853"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis token client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:53:5853"}
{"level":"error","message":"Redis session client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:53:5853"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:53:5853"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:53:5853"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:53:5853"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:53:5853"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:54:5854"}
{"level":"error","message":"Redis session client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:54:5854"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:54:5854"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:54:5854"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:54:5854"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:54:5854"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:55:5855"}
{"level":"error","message":"Redis session client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:55:5855"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:55:5855"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:55:5855"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:55:5855"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:55:5855"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:55:5855"}
{"level":"error","message":"Redis session client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:56:5856"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:56:5856"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:56:5856"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:56:5856"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:56:5856"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:56:5856"}
{"level":"error","message":"Redis session client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:56:5856"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:57:5857"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:57:5857"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:57:5857"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:57:5857"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:57:5857"}
{"level":"error","message":"Redis session client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:57:5857"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:58:5858"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:58:5858"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:58:5858"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:58:5858"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:58:5858"}
{"level":"error","message":"Redis session client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:58:5858"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:59:5859"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:59:5859"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:59:5859"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:59:5859"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:59:5859"}
{"level":"error","message":"Redis session client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:59:5859"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:59:5859"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis token client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:59:5859"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis token client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:59:5859"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:59:00:590"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:59:00:590"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:59:00:590"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:59:00:590"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:59:00:590"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis session client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:59:01:591"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis session client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:59:01:591"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:59:01:591"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis token client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:59:01:591"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis token client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:59:01:591"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:59:01:591"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:59:01:591"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:59:01:591"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis session client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:59:02:592"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis session client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:59:02:592"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:59:02:592"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:59:02:592"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:59:02:592"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:59:02:592"}
{"level":"error","message":"Redis session client error: ERR max number of clients reached","timestamp":"2025-06-20 18:59:02:592"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis token client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:59:03:593"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis token client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:59:03:593"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:59:03:593"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:59:03:593"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis session client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:59:04:594"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis session client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:59:04:594"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:59:04:594"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:59:04:594"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:59:04:594"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:59:04:594"}
{"level":"error","message":"Redis session client error: ERR max number of clients reached","timestamp":"2025-06-20 18:59:04:594"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:59:05:595"}
{"level":"error","message":"Redis session client error: ERR max number of clients reached","timestamp":"2025-06-20 18:59:05:595"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:59:05:595"}
{"level":"error","message":"Redis session client error: ERR max number of clients reached","timestamp":"2025-06-20 18:59:06:596"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:59:06:596"}
{"error":{"message":"Route /api/auth/health not found on this server","name":"Error","stack":"Error: Route /api/auth/health not found on this server\n    at notFoundHandler (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\middleware\\notFoundHandler.ts:9:17)\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts:188:3\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)"},"level":"error","message":"Error 404: Route /api/auth/health not found on this server","request":{"ip":"::1","method":"GET","url":"/api/auth/health","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"},"timestamp":"2025-06-20 19:24:09:249","user":null}
{"error":{"message":"Route /api/auth/health not found on this server","name":"Error","stack":"Error: Route /api/auth/health not found on this server\n    at notFoundHandler (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\middleware\\notFoundHandler.ts:9:17)\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts:188:3\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)"},"level":"error","message":"Error 404: Route /api/auth/health not found on this server","request":{"ip":"::1","method":"GET","url":"/api/auth/health","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"},"timestamp":"2025-06-20 19:27:30:2730","user":null}
{"error":{"message":"Route /api/properties/health not found on this server","name":"Error","stack":"Error: Route /api/properties/health not found on this server\n    at notFoundHandler (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\middleware\\notFoundHandler.ts:9:17)\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts:188:3\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)"},"level":"error","message":"Error 404: Route /api/properties/health not found on this server","request":{"ip":"::1","method":"GET","url":"/api/properties/health","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"},"timestamp":"2025-06-20 19:28:37:2837","user":null}
{"error":{"message":"Route /api/properties?limit=1 not found on this server","name":"Error","stack":"Error: Route /api/properties?limit=1 not found on this server\n    at notFoundHandler (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\middleware\\notFoundHandler.ts:9:17)\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts:188:3\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)"},"level":"error","message":"Error 404: Route /api/properties?limit=1 not found on this server","request":{"ip":"::1","method":"GET","url":"/api/properties?limit=1","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"},"timestamp":"2025-06-20 19:28:44:2844","user":null}
{"error":{"message":"Route /api/matches/health not found on this server","name":"Error","stack":"Error: Route /api/matches/health not found on this server\n    at notFoundHandler (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\middleware\\notFoundHandler.ts:9:17)\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts:188:3\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)"},"level":"error","message":"Error 404: Route /api/matches/health not found on this server","request":{"ip":"::1","method":"GET","url":"/api/matches/health","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"},"timestamp":"2025-06-20 19:31:13:3113","user":null}
{"error":{"message":"Route /api/properties?limit=1 not found on this server","name":"Error","stack":"Error: Route /api/properties?limit=1 not found on this server\n    at notFoundHandler (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\middleware\\notFoundHandler.ts:9:17)\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts:188:3\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)"},"level":"error","message":"Error 404: Route /api/properties?limit=1 not found on this server","request":{"ip":"::1","method":"GET","url":"/api/properties?limit=1","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"},"timestamp":"2025-06-20 19:32:13:3213","user":null}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis token client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 19:33:01:331"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 19:33:02:332"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis session client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 19:33:02:332"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 19:33:02:332"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis cache client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 19:33:05:335"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis cache client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 19:33:05:335"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis session client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 19:33:06:336"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis token client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 19:33:06:336"}
{"level":"error","message":"Redis token client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 19:33:06:336"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 19:33:07:337"}
{"level":"error","message":"Redis session client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 19:33:07:337"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 19:33:07:337"}
{"level":"error","message":"Redis cache client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 19:33:10:3310"}
{"level":"error","message":"Redis cache client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 19:33:10:3310"}
{"level":"error","message":"Redis session client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 19:33:11:3311"}
{"level":"error","message":"Redis token client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 19:33:11:3311"}
{"level":"error","message":"Redis token client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 19:33:11:3311"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 19:33:12:3312"}
{"level":"error","message":"Redis session client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 19:33:12:3312"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 19:33:12:3312"}
{"level":"error","message":"Redis cache client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 19:33:15:3315"}
{"level":"error","message":"Redis cache client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 19:33:15:3315"}
{"level":"error","message":"Redis session client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 19:33:16:3316"}
{"level":"error","message":"Redis token client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 19:33:16:3316"}
{"level":"error","message":"Redis token client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 19:33:16:3316"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 19:33:17:3317"}
{"level":"error","message":"Redis session client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 19:33:17:3317"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 19:33:17:3317"}
{"level":"error","message":"Redis cache client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 19:33:21:3321"}
{"level":"error","message":"Redis cache client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 19:33:21:3321"}
{"level":"error","message":"Redis session client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 19:33:21:3321"}
{"level":"error","message":"Redis token client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 19:33:21:3321"}
{"error":{"message":"Route /api/auth/health not found on this server","name":"Error","stack":"Error: Route /api/auth/health not found on this server\n    at notFoundHandler (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\middleware\\notFoundHandler.ts:9:17)\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts:188:3\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)"},"level":"error","message":"Error 404: Route /api/auth/health not found on this server","request":{"ip":"::1","method":"GET","url":"/api/auth/health","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"},"timestamp":"2025-06-20 19:33:46:3346","user":null}
{"error":{"message":"Route /api/test not found on this server","name":"Error","stack":"Error: Route /api/test not found on this server\n    at notFoundHandler (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\middleware\\notFoundHandler.ts:9:17)\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts:188:3\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)"},"level":"error","message":"Error 404: Route /api/test not found on this server","request":{"ip":"::1","method":"GET","url":"/api/test","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"},"timestamp":"2025-06-20 19:38:04:384","user":null}
{"error":{"message":"Route /api/test not found on this server","name":"Error","stack":"Error: Route /api/test not found on this server\n    at notFoundHandler (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\middleware\\notFoundHandler.ts:9:17)\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts:188:3\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)"},"level":"error","message":"Error 404: Route /api/test not found on this server","request":{"ip":"::1","method":"GET","url":"/api/test","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"},"timestamp":"2025-06-20 19:38:51:3851","user":null}
{"error":{"message":"Route /api/test not found on this server","name":"Error","stack":"Error: Route /api/test not found on this server\n    at notFoundHandler (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\middleware\\notFoundHandler.ts:9:17)\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts:188:3\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)"},"level":"error","message":"Error 404: Route /api/test not found on this server","request":{"ip":"::1","method":"GET","url":"/api/test","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"},"timestamp":"2025-06-20 19:47:10:4710","user":null}
