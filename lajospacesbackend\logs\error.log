{"code":"ESOCKET","command":"CONN","level":"error","library":"SSL routines","message":"Email transporter configuration error: 084A0000:error:0A00010B:SSL routines:ssl3_get_record:wrong version number:c:\\ws\\deps\\openssl\\openssl\\ssl\\record\\ssl3_record.c:355:\n","reason":"wrong version number","stack":"Error: 084A0000:error:0A00010B:SSL routines:ssl3_get_record:wrong version number:c:\\ws\\deps\\openssl\\openssl\\ssl\\record\\ssl3_record.c:355:\n","timestamp":"2025-06-20 13:09:34:934"}
{"code":"ESOCKET","command":"CONN","level":"error","library":"SSL routines","message":"Email transporter configuration error: E0550000:error:0A00010B:SSL routines:ssl3_get_record:wrong version number:c:\\ws\\deps\\openssl\\openssl\\ssl\\record\\ssl3_record.c:355:\n","reason":"wrong version number","stack":"Error: E0550000:error:0A00010B:SSL routines:ssl3_get_record:wrong version number:c:\\ws\\deps\\openssl\\openssl\\ssl\\record\\ssl3_record.c:355:\n","timestamp":"2025-06-20 13:13:44:1344"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 14:57:40:5740"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 15:00:10:010"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 15:00:15:015"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 15:00:20:020"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 15:00:25:025"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 15:00:31:031"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 15:00:36:036"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 15:00:42:042"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 15:00:47:047"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 15:00:53:053"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 15:00:58:058"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 15:01:04:14"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 15:07:43:743"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 15:07:49:749"}
{"error":{"message":"Route /favicon.ico not found on this server","name":"Error","stack":"Error: Route /favicon.ico not found on this server\n    at notFoundHandler (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\middleware\\notFoundHandler.ts:9:17)\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at logger (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\morgan\\index.js:144:5)\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)"},"level":"error","message":"Error 404: Route /favicon.ico not found on this server","request":{"ip":"::1","method":"GET","url":"/favicon.ico","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"},"timestamp":"2025-06-20 15:12:12:1212","user":null}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 15:22:56:2256"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 15:23:02:232"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 15:23:07:237"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 15:23:12:2312"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 15:24:58:2458"}
{"error":{"message":"Route /api/properties/health not found on this server","name":"Error","stack":"Error: Route /api/properties/health not found on this server\n    at notFoundHandler (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\middleware\\notFoundHandler.ts:9:17)\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at logger (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\morgan\\index.js:144:5)\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)"},"level":"error","message":"Error 404: Route /api/properties/health not found on this server","request":{"ip":"::1","method":"GET","url":"/api/properties/health","userAgent":"curl/8.6.0"},"timestamp":"2025-06-20 15:32:30:3230","user":null}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 15:32:57:3257"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 15:33:02:332"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 15:33:07:337"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 15:33:13:3313"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 15:33:18:3318"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 15:34:44:3444"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 15:40:20:4020"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 15:40:25:4025"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 15:40:31:4031"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 15:40:36:4036"}
{"cause":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{"ac-5ev0ybl-shard-00-00.5pikccg.mongodb.net:27017":{"$clusterTime":null,"address":"ac-5ev0ybl-shard-00-00.5pikccg.mongodb.net:27017","arbiters":[],"electionId":null,"error":null,"hosts":[],"iscryptd":false,"lastUpdateTime":13530346,"lastWriteDate":0,"logicalSessionTimeoutMinutes":null,"maxBsonObjectSize":null,"maxMessageSizeBytes":null,"maxWireVersion":0,"maxWriteBatchSize":null,"me":null,"minRoundTripTime":0,"minWireVersion":0,"passives":[],"primary":null,"roundTripTime":-1,"setName":null,"setVersion":null,"tags":{},"topologyVersion":null,"type":"Unknown"},"ac-5ev0ybl-shard-00-01.5pikccg.mongodb.net:27017":{"$clusterTime":null,"address":"ac-5ev0ybl-shard-00-01.5pikccg.mongodb.net:27017","arbiters":[],"electionId":null,"error":null,"hosts":[],"iscryptd":false,"lastUpdateTime":13530346,"lastWriteDate":0,"logicalSessionTimeoutMinutes":null,"maxBsonObjectSize":null,"maxMessageSizeBytes":null,"maxWireVersion":0,"maxWriteBatchSize":null,"me":null,"minRoundTripTime":0,"minWireVersion":0,"passives":[],"primary":null,"roundTripTime":-1,"setName":null,"setVersion":null,"tags":{},"topologyVersion":null,"type":"Unknown"},"ac-5ev0ybl-shard-00-02.5pikccg.mongodb.net:27017":{"$clusterTime":null,"address":"ac-5ev0ybl-shard-00-02.5pikccg.mongodb.net:27017","arbiters":[],"electionId":null,"error":null,"hosts":[],"iscryptd":false,"lastUpdateTime":13530346,"lastWriteDate":0,"logicalSessionTimeoutMinutes":null,"maxBsonObjectSize":null,"maxMessageSizeBytes":null,"maxWireVersion":0,"maxWriteBatchSize":null,"me":null,"minRoundTripTime":0,"minWireVersion":0,"passives":[],"primary":null,"roundTripTime":-1,"setName":null,"setVersion":null,"tags":{},"topologyVersion":null,"type":"Unknown"}},"setName":"atlas-be77u7-shard-0","stale":false,"type":"ReplicaSetNoPrimary"},"errorLabelSet":{},"level":"error","message":"❌ MongoDB connection failed: Could not connect to any servers in your MongoDB Atlas cluster. One common reason is that you're trying to access the database from an IP that isn't whitelisted. Make sure your current IP address is on your Atlas cluster's IP whitelist: https://www.mongodb.com/docs/atlas/security-whitelist/","reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{"ac-5ev0ybl-shard-00-00.5pikccg.mongodb.net:27017":{"$clusterTime":null,"address":"ac-5ev0ybl-shard-00-00.5pikccg.mongodb.net:27017","arbiters":[],"electionId":null,"error":null,"hosts":[],"iscryptd":false,"lastUpdateTime":13530346,"lastWriteDate":0,"logicalSessionTimeoutMinutes":null,"maxBsonObjectSize":null,"maxMessageSizeBytes":null,"maxWireVersion":0,"maxWriteBatchSize":null,"me":null,"minRoundTripTime":0,"minWireVersion":0,"passives":[],"primary":null,"roundTripTime":-1,"setName":null,"setVersion":null,"tags":{},"topologyVersion":null,"type":"Unknown"},"ac-5ev0ybl-shard-00-01.5pikccg.mongodb.net:27017":{"$clusterTime":null,"address":"ac-5ev0ybl-shard-00-01.5pikccg.mongodb.net:27017","arbiters":[],"electionId":null,"error":null,"hosts":[],"iscryptd":false,"lastUpdateTime":13530346,"lastWriteDate":0,"logicalSessionTimeoutMinutes":null,"maxBsonObjectSize":null,"maxMessageSizeBytes":null,"maxWireVersion":0,"maxWriteBatchSize":null,"me":null,"minRoundTripTime":0,"minWireVersion":0,"passives":[],"primary":null,"roundTripTime":-1,"setName":null,"setVersion":null,"tags":{},"topologyVersion":null,"type":"Unknown"},"ac-5ev0ybl-shard-00-02.5pikccg.mongodb.net:27017":{"$clusterTime":null,"address":"ac-5ev0ybl-shard-00-02.5pikccg.mongodb.net:27017","arbiters":[],"electionId":null,"error":null,"hosts":[],"iscryptd":false,"lastUpdateTime":13530346,"lastWriteDate":0,"logicalSessionTimeoutMinutes":null,"maxBsonObjectSize":null,"maxMessageSizeBytes":null,"maxWireVersion":0,"maxWriteBatchSize":null,"me":null,"minRoundTripTime":0,"minWireVersion":0,"passives":[],"primary":null,"roundTripTime":-1,"setName":null,"setVersion":null,"tags":{},"topologyVersion":null,"type":"Unknown"}},"setName":"atlas-be77u7-shard-0","stale":false,"type":"ReplicaSetNoPrimary"},"stack":"MongooseServerSelectionError: Could not connect to any servers in your MongoDB Atlas cluster. One common reason is that you're trying to access the database from an IP that isn't whitelisted. Make sure your current IP address is on your Atlas cluster's IP whitelist: https://www.mongodb.com/docs/atlas/security-whitelist/\n    at _handleConnectionErrors (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\mongoose\\lib\\connection.js:1165:11)\n    at NativeConnection.openUri (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\mongoose\\lib\\connection.js:1096:11)\n    at async connectDatabase (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\config\\database.ts:29:5)\n    at async startServer (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts:214:5)","timestamp":"2025-06-20 16:24:14:2414"}
{"code":"MODULE_NOT_FOUND","level":"error","message":"Failed to setup routes: Cannot find module 'sharp'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts","requireStack":["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts"],"stack":"Error: Cannot find module 'sharp'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\controllers\\upload.controller.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\upload.routes.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1144:15)\n    at Function.Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js:811:30)\n    at Function.Module._load (node:internal/modules/cjs/loader:985:27)\n    at Module.require (node:internal/modules/cjs/loader:1235:19)\n    at require (node:internal/modules/helpers:176:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\services\\imageOptimizationService.ts:1:1)\n    at Module._compile (node:internal/modules/cjs/loader:1376:14)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1621:12)","timestamp":"2025-06-20 17:56:30:5630"}
{"code":"MODULE_NOT_FOUND","level":"error","message":"Failed to setup routes: Cannot find module '../middleware/authMiddleware'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\admin.routes.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts","requireStack":["C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\admin.routes.ts","C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts"],"stack":"Error: Cannot find module '../middleware/authMiddleware'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\admin.routes.ts\n- C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1144:15)\n    at Function.Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js:811:30)\n    at Function.Module._load (node:internal/modules/cjs/loader:985:27)\n    at Module.require (node:internal/modules/cjs/loader:1235:19)\n    at require (node:internal/modules/helpers:176:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\routes\\admin.routes.ts:2:1)\n    at Module._compile (node:internal/modules/cjs/loader:1376:14)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1435:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\ts-node\\src\\index.ts:1621:12)","timestamp":"2025-06-20 17:57:26:5726"}
{"error":{"message":"Route /api/auth/health not found on this server","name":"Error","stack":"Error: Route /api/auth/health not found on this server\n    at notFoundHandler (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\middleware\\notFoundHandler.ts:9:17)\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts:180:3\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)"},"level":"error","message":"Error 404: Route /api/auth/health not found on this server","request":{"ip":"::1","method":"GET","url":"/api/auth/health","userAgent":"curl/8.6.0"},"timestamp":"2025-06-20 18:00:37:037","user":null}
{"error":{"message":"Route /api/auth/health not found on this server","name":"Error","stack":"Error: Route /api/auth/health not found on this server\n    at notFoundHandler (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\middleware\\notFoundHandler.ts:9:17)\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts:180:3\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)"},"level":"error","message":"Error 404: Route /api/auth/health not found on this server","request":{"ip":"::1","method":"GET","url":"/api/auth/health","userAgent":"curl/8.6.0"},"timestamp":"2025-06-20 18:00:51:051","user":null}
{"error":{"message":"Route /api/auth/health not found on this server","name":"Error","stack":"Error: Route /api/auth/health not found on this server\n    at notFoundHandler (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\middleware\\notFoundHandler.ts:9:17)\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts:180:3\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)"},"level":"error","message":"Error 404: Route /api/auth/health not found on this server","request":{"ip":"::1","method":"GET","url":"/api/auth/health","userAgent":"curl/8.6.0"},"timestamp":"2025-06-20 18:01:08:18","user":null}
{"error":{"message":"Route /api/users/health not found on this server","name":"Error","stack":"Error: Route /api/users/health not found on this server\n    at notFoundHandler (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\middleware\\notFoundHandler.ts:9:17)\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts:180:3\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)"},"level":"error","message":"Error 404: Route /api/users/health not found on this server","request":{"ip":"::1","method":"GET","url":"/api/users/health","userAgent":"curl/8.6.0"},"timestamp":"2025-06-20 18:01:28:128","user":null}
{"error":{"message":"Route /api/auth/health not found on this server","name":"Error","stack":"Error: Route /api/auth/health not found on this server\n    at notFoundHandler (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\middleware\\notFoundHandler.ts:9:17)\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts:180:3\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)"},"level":"error","message":"Error 404: Route /api/auth/health not found on this server","request":{"ip":"::1","method":"GET","url":"/api/auth/health","userAgent":"curl/8.6.0"},"timestamp":"2025-06-20 18:03:45:345","user":null}
{"error":{"message":"Route /api/auth/health not found on this server","name":"Error","stack":"Error: Route /api/auth/health not found on this server\n    at notFoundHandler (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\middleware\\notFoundHandler.ts:9:17)\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts:180:3\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)"},"level":"error","message":"Error 404: Route /api/auth/health not found on this server","request":{"ip":"::1","method":"GET","url":"/api/auth/health","userAgent":"curl/8.6.0"},"timestamp":"2025-06-20 18:05:22:522","user":null}
{"error":{"message":"Route /api/auth/health not found on this server","name":"Error","stack":"Error: Route /api/auth/health not found on this server\n    at notFoundHandler (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\middleware\\notFoundHandler.ts:9:17)\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts:188:3\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)"},"level":"error","message":"Error 404: Route /api/auth/health not found on this server","request":{"ip":"::1","method":"GET","url":"/api/auth/health","userAgent":"curl/8.6.0"},"timestamp":"2025-06-20 18:06:40:640","user":null}
{"error":{"message":"Route /api/properties/health not found on this server","name":"Error","stack":"Error: Route /api/properties/health not found on this server\n    at notFoundHandler (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\middleware\\notFoundHandler.ts:9:17)\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts:188:3\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)"},"level":"error","message":"Error 404: Route /api/properties/health not found on this server","request":{"ip":"::1","method":"GET","url":"/api/properties/health","userAgent":"curl/8.6.0"},"timestamp":"2025-06-20 18:09:58:958","user":null}
{"error":{"message":"Route /api/properties/health not found on this server","name":"Error","stack":"Error: Route /api/properties/health not found on this server\n    at notFoundHandler (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\middleware\\notFoundHandler.ts:9:17)\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts:188:3\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)"},"level":"error","message":"Error 404: Route /api/properties/health not found on this server","request":{"ip":"::1","method":"GET","url":"/api/properties/health","userAgent":"curl/8.6.0"},"timestamp":"2025-06-20 18:10:12:1012","user":null}
{"error":{"message":"Route /api/properties/health not found on this server","name":"Error","stack":"Error: Route /api/properties/health not found on this server\n    at notFoundHandler (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\middleware\\notFoundHandler.ts:9:17)\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts:188:3\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)"},"level":"error","message":"Error 404: Route /api/properties/health not found on this server","request":{"ip":"::1","method":"GET","url":"/api/properties/health","userAgent":"curl/8.6.0"},"timestamp":"2025-06-20 18:13:39:1339","user":null}
{"error":{"message":"Route /api/properties?limit=1 not found on this server","name":"Error","stack":"Error: Route /api/properties?limit=1 not found on this server\n    at notFoundHandler (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\middleware\\notFoundHandler.ts:9:17)\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts:188:3\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)"},"level":"error","message":"Error 404: Route /api/properties?limit=1 not found on this server","request":{"ip":"::1","method":"GET","url":"/api/properties?limit=1","userAgent":"curl/8.6.0"},"timestamp":"2025-06-20 18:13:58:1358","user":null}
{"error":{"message":"Route /api/properties?limit=1 not found on this server","name":"Error","stack":"Error: Route /api/properties?limit=1 not found on this server\n    at notFoundHandler (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\middleware\\notFoundHandler.ts:9:17)\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts:188:3\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)"},"level":"error","message":"Error 404: Route /api/properties?limit=1 not found on this server","request":{"ip":"::1","method":"GET","url":"/api/properties?limit=1","userAgent":"curl/8.6.0"},"timestamp":"2025-06-20 18:14:41:1441","user":null}
