{"code":"ESOCKET","command":"CONN","level":"error","library":"SSL routines","message":"Email transporter configuration error: 084A0000:error:0A00010B:SSL routines:ssl3_get_record:wrong version number:c:\\ws\\deps\\openssl\\openssl\\ssl\\record\\ssl3_record.c:355:\n","reason":"wrong version number","stack":"Error: 084A0000:error:0A00010B:SSL routines:ssl3_get_record:wrong version number:c:\\ws\\deps\\openssl\\openssl\\ssl\\record\\ssl3_record.c:355:\n","timestamp":"2025-06-20 13:09:34:934"}
{"code":"ESOCKET","command":"CONN","level":"error","library":"SSL routines","message":"Email transporter configuration error: E0550000:error:0A00010B:SSL routines:ssl3_get_record:wrong version number:c:\\ws\\deps\\openssl\\openssl\\ssl\\record\\ssl3_record.c:355:\n","reason":"wrong version number","stack":"Error: E0550000:error:0A00010B:SSL routines:ssl3_get_record:wrong version number:c:\\ws\\deps\\openssl\\openssl\\ssl\\record\\ssl3_record.c:355:\n","timestamp":"2025-06-20 13:13:44:1344"}
{"level":"info","message":"✅ Email service ready","timestamp":"2025-06-20 13:49:29:4929"}
{"level":"info","message":"✅ Email service ready","timestamp":"2025-06-20 13:57:33:5733"}
{"level":"info","message":"✅ Email service ready","timestamp":"2025-06-20 13:58:57:5857"}
{"level":"info","message":"✅ Email service ready","timestamp":"2025-06-20 14:00:16:016"}
{"level":"info","message":"✅ Email service ready","timestamp":"2025-06-20 14:03:10:310"}
{"level":"info","message":"✅ Email service ready","timestamp":"2025-06-20 14:05:58:558"}
{"level":"info","message":"✅ Email service ready","timestamp":"2025-06-20 14:16:23:1623"}
{"level":"info","message":"✅ Email service ready","timestamp":"2025-06-20 14:34:49:3449"}
{"level":"info","message":"✅ Email service ready","timestamp":"2025-06-20 14:54:44:5444"}
{"level":"info","message":"🚀 Starting server initialization...","timestamp":"2025-06-20 14:57:18:5718"}
{"level":"info","message":"🚀 Starting LajoSpaces Backend Server...","timestamp":"2025-06-20 14:57:18:5718"}
{"level":"info","message":"📊 Connecting to MongoDB...","timestamp":"2025-06-20 14:57:18:5718"}
{"level":"info","message":"📊 Connecting to MongoDB...","timestamp":"2025-06-20 14:57:18:5718"}
{"level":"info","message":"✅ Email service ready","timestamp":"2025-06-20 14:57:23:5723"}
{"level":"info","message":"📊 Mongoose connected to MongoDB","timestamp":"2025-06-20 14:57:23:5723"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-20 14:57:23:5723"}
{"level":"info","message":"📊 Connected to database: lajospaces","timestamp":"2025-06-20 14:57:23:5723"}
{"level":"info","message":"✅ MongoDB connection completed","timestamp":"2025-06-20 14:57:23:5723"}
{"level":"info","message":"🔴 Connecting to Redis...","timestamp":"2025-06-20 14:57:23:5723"}
{"level":"info","message":"🔴 Connecting to Redis...","timestamp":"2025-06-20 14:57:23:5723"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 14:57:24:5724"}
{"level":"info","message":"✅ Redis client ready","timestamp":"2025-06-20 14:57:24:5724"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-20 14:57:25:5725"}
{"level":"info","message":"✅ Redis connection completed","timestamp":"2025-06-20 14:57:25:5725"}
{"level":"info","message":"⚙️ Initializing services...","timestamp":"2025-06-20 14:57:25:5725"}
{"level":"info","message":"All services initialized successfully (services temporarily disabled)","timestamp":"2025-06-20 14:57:25:5725"}
{"level":"info","message":"✅ Services initialization completed","timestamp":"2025-06-20 14:57:25:5725"}
{"level":"info","message":"🛡️ Setting up rate limiting...","timestamp":"2025-06-20 14:57:25:5725"}
{"level":"info","message":"💬 Initializing Socket.IO service...","timestamp":"2025-06-20 14:57:25:5725"}
{"level":"info","message":"✅ Socket.IO service initialized","timestamp":"2025-06-20 14:57:25:5725"}
{"level":"info","message":"🚀 LajoSpaces Backend Server running on port 5000","timestamp":"2025-06-20 14:57:25:5725"}
{"level":"info","message":"🌍 Environment: development","timestamp":"2025-06-20 14:57:25:5725"}
{"level":"info","message":"📊 Health check: http://localhost:5000/health","timestamp":"2025-06-20 14:57:25:5725"}
{"level":"info","message":"🔗 API Base URL: http://localhost:5000/api","timestamp":"2025-06-20 14:57:25:5725"}
{"level":"info","message":"💬 Socket.IO enabled for real-time messaging","timestamp":"2025-06-20 14:57:25:5725"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 14:57:40:5740"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 14:57:43:5743"}
{"level":"info","message":"✅ Redis client ready","timestamp":"2025-06-20 14:57:43:5743"}
{"level":"info","message":"🚀 Starting server initialization...","timestamp":"2025-06-20 14:58:43:5843"}
{"level":"info","message":"🚀 Starting LajoSpaces Backend Server...","timestamp":"2025-06-20 14:58:43:5843"}
{"level":"info","message":"📊 Connecting to MongoDB...","timestamp":"2025-06-20 14:58:43:5843"}
{"level":"info","message":"📊 Connecting to MongoDB...","timestamp":"2025-06-20 14:58:43:5843"}
{"level":"info","message":"✅ Email service ready","timestamp":"2025-06-20 14:58:46:5846"}
{"level":"info","message":"📊 Mongoose connected to MongoDB","timestamp":"2025-06-20 14:58:46:5846"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-20 14:58:46:5846"}
{"level":"info","message":"📊 Connected to database: lajospaces","timestamp":"2025-06-20 14:58:46:5846"}
{"level":"info","message":"✅ MongoDB connection completed","timestamp":"2025-06-20 14:58:46:5846"}
{"level":"info","message":"🔴 Connecting to Redis...","timestamp":"2025-06-20 14:58:46:5846"}
{"level":"info","message":"🔴 Connecting to Redis...","timestamp":"2025-06-20 14:58:46:5846"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 14:58:46:5846"}
{"level":"info","message":"✅ Redis client ready","timestamp":"2025-06-20 14:58:47:5847"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-20 14:58:47:5847"}
{"level":"info","message":"✅ Redis connection completed","timestamp":"2025-06-20 14:58:47:5847"}
{"level":"info","message":"⚙️ Initializing services...","timestamp":"2025-06-20 14:58:47:5847"}
{"level":"info","message":"All services initialized successfully (services temporarily disabled)","timestamp":"2025-06-20 14:58:47:5847"}
{"level":"info","message":"✅ Services initialization completed","timestamp":"2025-06-20 14:58:47:5847"}
{"level":"info","message":"🛡️ Setting up rate limiting...","timestamp":"2025-06-20 14:58:47:5847"}
{"level":"info","message":"💬 Initializing Socket.IO service...","timestamp":"2025-06-20 14:58:47:5847"}
{"level":"info","message":"✅ Socket.IO service initialized","timestamp":"2025-06-20 14:58:47:5847"}
{"level":"info","message":"SIGINT received, shutting down gracefully","timestamp":"2025-06-20 14:59:00:590"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 15:00:10:010"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 15:00:15:015"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 15:00:20:020"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 15:00:25:025"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 15:00:31:031"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 15:00:36:036"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 15:00:42:042"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 15:00:47:047"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 15:00:53:053"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 15:00:58:058"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 15:01:04:14"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 15:01:05:15"}
{"level":"info","message":"✅ Redis client ready","timestamp":"2025-06-20 15:01:05:15"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 15:07:43:743"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 15:07:49:749"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 15:07:52:752"}
{"level":"info","message":"✅ Redis client ready","timestamp":"2025-06-20 15:07:53:753"}
{"level":"info","message":"::1 - - [20/Jun/2025:14:12:11 +0000] \"GET /api/docs HTTP/1.1\" 301 158 \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","timestamp":"2025-06-20 15:12:11:1211"}
{"level":"info","message":"::1 - - [20/Jun/2025:14:12:11 +0000] \"GET /api/docs/ HTTP/1.1\" 200 - \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","timestamp":"2025-06-20 15:12:11:1211"}
{"level":"info","message":"::1 - - [20/Jun/2025:14:12:11 +0000] \"GET /api/docs/swagger-ui-init.js HTTP/1.1\" 200 - \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","timestamp":"2025-06-20 15:12:11:1211"}
{"level":"info","message":"::1 - - [20/Jun/2025:14:12:11 +0000] \"GET /api/docs/swagger-ui.css HTTP/1.1\" 200 - \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","timestamp":"2025-06-20 15:12:11:1211"}
{"level":"info","message":"::1 - - [20/Jun/2025:14:12:11 +0000] \"GET /api/docs/swagger-ui-standalone-preset.js HTTP/1.1\" 200 - \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","timestamp":"2025-06-20 15:12:11:1211"}
{"level":"info","message":"::1 - - [20/Jun/2025:14:12:11 +0000] \"GET /api/docs/swagger-ui-bundle.js HTTP/1.1\" 200 - \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","timestamp":"2025-06-20 15:12:11:1211"}
{"error":{"message":"Route /favicon.ico not found on this server","name":"Error","stack":"Error: Route /favicon.ico not found on this server\n    at notFoundHandler (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\middleware\\notFoundHandler.ts:9:17)\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at logger (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\morgan\\index.js:144:5)\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)"},"level":"error","message":"Error 404: Route /favicon.ico not found on this server","request":{"ip":"::1","method":"GET","url":"/favicon.ico","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"},"timestamp":"2025-06-20 15:12:12:1212","user":null}
{"level":"info","message":"::1 - - [20/Jun/2025:14:12:12 +0000] \"GET /favicon.ico HTTP/1.1\" 404 332 \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"","timestamp":"2025-06-20 15:12:12:1212"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 15:22:56:2256"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 15:23:02:232"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 15:23:07:237"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 15:23:12:2312"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 15:23:13:2313"}
{"level":"info","message":"✅ Redis client ready","timestamp":"2025-06-20 15:23:13:2313"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 15:24:58:2458"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 15:25:01:251"}
{"level":"info","message":"✅ Redis client ready","timestamp":"2025-06-20 15:25:02:252"}
{"level":"info","message":"::1 - - [20/Jun/2025:14:26:52 +0000] \"GET /api HTTP/1.1\" 200 502 \"-\" \"curl/8.6.0\"","timestamp":"2025-06-20 15:26:52:2652"}
{"level":"info","message":"::1 - - [20/Jun/2025:14:27:31 +0000] \"GET /api HTTP/1.1\" 200 502 \"-\" \"curl/8.6.0\"","timestamp":"2025-06-20 15:27:31:2731"}
{"level":"info","message":"::1 - - [20/Jun/2025:14:28:10 +0000] \"GET /api/auth/health HTTP/1.1\" 200 211 \"-\" \"curl/8.6.0\"","timestamp":"2025-06-20 15:28:10:2810"}
{"error":{"message":"Route /api/properties/health not found on this server","name":"Error","stack":"Error: Route /api/properties/health not found on this server\n    at notFoundHandler (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\middleware\\notFoundHandler.ts:9:17)\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at logger (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\morgan\\index.js:144:5)\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)"},"level":"error","message":"Error 404: Route /api/properties/health not found on this server","request":{"ip":"::1","method":"GET","url":"/api/properties/health","userAgent":"curl/8.6.0"},"timestamp":"2025-06-20 15:32:30:3230","user":null}
{"level":"info","message":"::1 - - [20/Jun/2025:14:32:30 +0000] \"GET /api/properties/health HTTP/1.1\" 404 362 \"-\" \"curl/8.6.0\"","timestamp":"2025-06-20 15:32:30:3230"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 15:32:57:3257"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 15:33:02:332"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 15:33:07:337"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 15:33:13:3313"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 15:33:18:3318"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 15:33:23:3323"}
{"level":"info","message":"✅ Redis client ready","timestamp":"2025-06-20 15:33:23:3323"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 15:34:44:3444"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 15:34:46:3446"}
{"level":"info","message":"✅ Redis client ready","timestamp":"2025-06-20 15:34:46:3446"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 15:40:20:4020"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 15:40:25:4025"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 15:40:31:4031"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 15:40:36:4036"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 15:40:40:4040"}
{"level":"info","message":"✅ Redis client ready","timestamp":"2025-06-20 15:40:40:4040"}
{"level":"info","message":"✅ Email service ready","timestamp":"2025-06-20 15:40:46:4046"}
{"level":"info","message":"✅ Email service ready","timestamp":"2025-06-20 15:43:49:4349"}
{"level":"info","message":"✅ Email service ready","timestamp":"2025-06-20 16:07:46:746"}
{"level":"info","message":"✅ Email service ready","timestamp":"2025-06-20 16:08:53:853"}
{"level":"info","message":"✅ Email service ready","timestamp":"2025-06-20 16:13:17:1317"}
{"level":"info","message":"📧 Email service initialized (verification disabled for testing)","timestamp":"2025-06-20 16:17:22:1722"}
{"level":"info","message":"📧 Email service initialized (verification disabled for testing)","timestamp":"2025-06-20 16:18:27:1827"}
{"level":"info","message":"🚀 Starting server initialization...","timestamp":"2025-06-20 16:18:28:1828"}
{"level":"info","message":"🚀 Starting LajoSpaces Backend Server...","timestamp":"2025-06-20 16:18:28:1828"}
{"level":"info","message":"📊 Connecting to MongoDB...","timestamp":"2025-06-20 16:18:28:1828"}
{"level":"info","message":"📊 Connecting to MongoDB...","timestamp":"2025-06-20 16:18:28:1828"}
{"level":"info","message":"📊 Mongoose connected to MongoDB","timestamp":"2025-06-20 16:18:31:1831"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-20 16:18:31:1831"}
{"level":"info","message":"📊 Connected to database: lajospaces","timestamp":"2025-06-20 16:18:31:1831"}
{"level":"info","message":"✅ MongoDB connection completed","timestamp":"2025-06-20 16:18:31:1831"}
{"level":"info","message":"🔴 Connecting to Redis...","timestamp":"2025-06-20 16:18:31:1831"}
{"level":"info","message":"🔴 Connecting to Redis...","timestamp":"2025-06-20 16:18:31:1831"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 16:18:31:1831"}
{"level":"info","message":"✅ Redis client ready","timestamp":"2025-06-20 16:18:31:1831"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-20 16:18:32:1832"}
{"level":"info","message":"✅ Redis connection completed","timestamp":"2025-06-20 16:18:32:1832"}
{"level":"info","message":"⚙️ Initializing services...","timestamp":"2025-06-20 16:18:32:1832"}
{"level":"info","message":"All services initialized successfully (temporarily minimal)","timestamp":"2025-06-20 16:18:32:1832"}
{"level":"info","message":"✅ Services initialization completed","timestamp":"2025-06-20 16:18:32:1832"}
{"level":"info","message":"💬 Initializing Socket.IO service...","timestamp":"2025-06-20 16:18:32:1832"}
{"level":"info","message":"✅ Socket.IO service initialized","timestamp":"2025-06-20 16:18:32:1832"}
{"level":"info","message":"🚀 LajoSpaces Backend Server running on port 5000","timestamp":"2025-06-20 16:18:32:1832"}
{"level":"info","message":"🌍 Environment: development","timestamp":"2025-06-20 16:18:32:1832"}
{"level":"info","message":"📊 Health check: http://localhost:5000/health","timestamp":"2025-06-20 16:18:32:1832"}
{"level":"info","message":"🔗 API Base URL: http://localhost:5000/api","timestamp":"2025-06-20 16:18:32:1832"}
{"level":"info","message":"💬 Socket.IO enabled for real-time messaging","timestamp":"2025-06-20 16:18:32:1832"}
{"level":"info","message":"::1 - - [20/Jun/2025:15:20:06 +0000] \"GET /api HTTP/1.1\" 200 502 \"-\" \"curl/8.6.0\"","timestamp":"2025-06-20 16:20:06:206"}
{"level":"info","message":"::1 - - [20/Jun/2025:15:20:17 +0000] \"GET /api/auth/health HTTP/1.1\" 200 211 \"-\" \"curl/8.6.0\"","timestamp":"2025-06-20 16:20:17:2017"}
{"level":"info","message":"📧 Email service initialized (verification disabled for testing)","timestamp":"2025-06-20 16:21:39:2139"}
{"level":"info","message":"📧 Email service initialized (verification disabled for testing)","timestamp":"2025-06-20 16:24:07:247"}
{"level":"info","message":"🚀 Starting server initialization...","timestamp":"2025-06-20 16:24:08:248"}
{"level":"info","message":"🚀 Starting LajoSpaces Backend Server...","timestamp":"2025-06-20 16:24:08:248"}
{"level":"info","message":"📊 Connecting to MongoDB...","timestamp":"2025-06-20 16:24:08:248"}
{"level":"info","message":"📊 Connecting to MongoDB...","timestamp":"2025-06-20 16:24:08:248"}
{"level":"info","message":"📊 Mongoose disconnected from MongoDB","timestamp":"2025-06-20 16:24:14:2414"}
{"level":"info","message":"📧 Email service initialized (verification disabled for testing)","timestamp":"2025-06-20 16:25:15:2515"}
{"level":"info","message":"🚀 Starting server initialization...","timestamp":"2025-06-20 16:25:17:2517"}
{"level":"info","message":"🚀 Starting LajoSpaces Backend Server...","timestamp":"2025-06-20 16:25:17:2517"}
{"level":"info","message":"📊 Connecting to MongoDB...","timestamp":"2025-06-20 16:25:17:2517"}
{"level":"info","message":"📊 Connecting to MongoDB...","timestamp":"2025-06-20 16:25:17:2517"}
{"level":"info","message":"📊 Mongoose connected to MongoDB","timestamp":"2025-06-20 16:25:20:2520"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-20 16:25:20:2520"}
{"level":"info","message":"📊 Connected to database: lajospaces","timestamp":"2025-06-20 16:25:20:2520"}
{"level":"info","message":"✅ MongoDB connection completed","timestamp":"2025-06-20 16:25:20:2520"}
{"level":"info","message":"🔴 Connecting to Redis...","timestamp":"2025-06-20 16:25:20:2520"}
{"level":"info","message":"🔴 Connecting to Redis...","timestamp":"2025-06-20 16:25:20:2520"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 16:25:21:2521"}
{"level":"info","message":"✅ Redis client ready","timestamp":"2025-06-20 16:25:21:2521"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-20 16:25:21:2521"}
{"level":"info","message":"✅ Redis connection completed","timestamp":"2025-06-20 16:25:21:2521"}
{"level":"info","message":"⚙️ Initializing services...","timestamp":"2025-06-20 16:25:21:2521"}
{"level":"info","message":"All services initialized successfully (temporarily minimal)","timestamp":"2025-06-20 16:25:21:2521"}
{"level":"info","message":"✅ Services initialization completed","timestamp":"2025-06-20 16:25:21:2521"}
{"level":"info","message":"💬 Initializing Socket.IO service...","timestamp":"2025-06-20 16:25:21:2521"}
{"level":"info","message":"✅ Socket.IO service initialized","timestamp":"2025-06-20 16:25:21:2521"}
{"level":"info","message":"🚀 LajoSpaces Backend Server running on port 5000","timestamp":"2025-06-20 16:25:21:2521"}
{"level":"info","message":"🌍 Environment: development","timestamp":"2025-06-20 16:25:21:2521"}
{"level":"info","message":"📊 Health check: http://localhost:5000/health","timestamp":"2025-06-20 16:25:21:2521"}
{"level":"info","message":"🔗 API Base URL: http://localhost:5000/api","timestamp":"2025-06-20 16:25:21:2521"}
{"level":"info","message":"💬 Socket.IO enabled for real-time messaging","timestamp":"2025-06-20 16:25:21:2521"}
{"level":"info","message":"::1 - - [20/Jun/2025:15:26:06 +0000] \"GET /api/profiles/health HTTP/1.1\" 200 237 \"-\" \"curl/8.6.0\"","timestamp":"2025-06-20 16:26:06:266"}
{"level":"info","message":"📧 Email service initialized (verification disabled for testing)","timestamp":"2025-06-20 16:34:58:3458"}
{"level":"info","message":"📧 Email service initialized (verification disabled for testing)","timestamp":"2025-06-20 16:37:50:3750"}
{"level":"info","message":"📧 Email service initialized (verification disabled for testing)","timestamp":"2025-06-20 16:44:05:445"}
{"level":"info","message":"📧 Email service initialized (verification disabled for testing)","timestamp":"2025-06-20 16:49:49:4949"}
{"level":"info","message":"📧 Email service initialized (verification disabled for testing)","timestamp":"2025-06-20 16:55:18:5518"}
{"level":"info","message":"📧 Email service initialized (verification disabled for testing)","timestamp":"2025-06-20 16:57:38:5738"}
{"level":"info","message":"📧 Email service initialized (verification disabled for testing)","timestamp":"2025-06-20 16:58:44:5844"}
{"level":"info","message":"🚀 Starting server initialization...","timestamp":"2025-06-20 17:37:20:3720"}
{"level":"info","message":"🚀 Starting LajoSpaces Backend Server...","timestamp":"2025-06-20 17:37:20:3720"}
{"level":"info","message":"⚙️ Initializing services...","timestamp":"2025-06-20 17:37:20:3720"}
{"level":"info","message":"✅ Services initialization completed (skipped for testing)","timestamp":"2025-06-20 17:37:20:3720"}
{"level":"info","message":"🛡️ Setting up rate limiting...","timestamp":"2025-06-20 17:37:20:3720"}
{"level":"info","message":"🚀 LajoSpaces Backend Server running on port 5000","timestamp":"2025-06-20 17:37:20:3720"}
{"level":"info","message":"🌍 Environment: development","timestamp":"2025-06-20 17:37:20:3720"}
{"level":"info","message":"📊 Health check: http://localhost:5000/health","timestamp":"2025-06-20 17:37:20:3720"}
{"level":"info","message":"🔗 API Base URL: http://localhost:5000/api","timestamp":"2025-06-20 17:37:20:3720"}
{"level":"info","message":"⚠️ Socket.IO temporarily disabled for testing","timestamp":"2025-06-20 17:37:20:3720"}
{"level":"info","message":"::1 - - [20/Jun/2025:16:43:30 +0000] \"GET /health HTTP/1.1\" 200 233 \"-\" \"curl/8.6.0\"","timestamp":"2025-06-20 17:43:30:4330"}
{"level":"info","message":"🚀 Starting server initialization...","timestamp":"2025-06-20 17:56:26:5626"}
{"level":"info","message":"🚀 Starting LajoSpaces Backend Server...","timestamp":"2025-06-20 17:56:26:5626"}
{"level":"info","message":"📊 Connecting to MongoDB...","timestamp":"2025-06-20 17:56:26:5626"}
{"level":"info","message":"📊 Connecting to MongoDB...","timestamp":"2025-06-20 17:56:26:5626"}
{"level":"info","message":"📊 Mongoose connected to MongoDB","timestamp":"2025-06-20 17:56:28:5628"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-20 17:56:28:5628"}
{"level":"info","message":"📊 Connected to database: lajospaces","timestamp":"2025-06-20 17:56:28:5628"}
{"level":"info","message":"✅ MongoDB connection completed","timestamp":"2025-06-20 17:56:28:5628"}
{"level":"info","message":"🔴 Connecting to Redis...","timestamp":"2025-06-20 17:56:28:5628"}
{"level":"info","message":"🔴 Connecting to Redis...","timestamp":"2025-06-20 17:56:28:5628"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 17:56:29:5629"}
{"level":"info","message":"✅ Redis client ready","timestamp":"2025-06-20 17:56:29:5629"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-20 17:56:29:5629"}
{"level":"info","message":"🚀 Starting server initialization...","timestamp":"2025-06-20 17:57:20:5720"}
{"level":"info","message":"🚀 Starting LajoSpaces Backend Server...","timestamp":"2025-06-20 17:57:20:5720"}
{"level":"info","message":"📊 Connecting to MongoDB...","timestamp":"2025-06-20 17:57:20:5720"}
{"level":"info","message":"📊 Connecting to MongoDB...","timestamp":"2025-06-20 17:57:20:5720"}
{"level":"info","message":"📊 Mongoose connected to MongoDB","timestamp":"2025-06-20 17:57:24:5724"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-20 17:57:24:5724"}
{"level":"info","message":"📊 Connected to database: lajospaces","timestamp":"2025-06-20 17:57:24:5724"}
{"level":"info","message":"✅ MongoDB connection completed","timestamp":"2025-06-20 17:57:24:5724"}
{"level":"info","message":"🔴 Connecting to Redis...","timestamp":"2025-06-20 17:57:24:5724"}
{"level":"info","message":"🔴 Connecting to Redis...","timestamp":"2025-06-20 17:57:24:5724"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 17:57:24:5724"}
{"level":"info","message":"✅ Redis client ready","timestamp":"2025-06-20 17:57:24:5724"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-20 17:57:24:5724"}
{"level":"info","message":"🚀 Starting server initialization...","timestamp":"2025-06-20 17:59:37:5937"}
{"level":"info","message":"🚀 Starting LajoSpaces Backend Server...","timestamp":"2025-06-20 17:59:37:5937"}
{"level":"info","message":"📊 Connecting to MongoDB...","timestamp":"2025-06-20 17:59:37:5937"}
{"level":"info","message":"📊 Connecting to MongoDB...","timestamp":"2025-06-20 17:59:37:5937"}
{"level":"info","message":"📊 Mongoose connected to MongoDB","timestamp":"2025-06-20 17:59:40:5940"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-20 17:59:40:5940"}
{"level":"info","message":"📊 Connected to database: lajospaces","timestamp":"2025-06-20 17:59:40:5940"}
{"level":"info","message":"✅ MongoDB connection completed","timestamp":"2025-06-20 17:59:40:5940"}
{"level":"info","message":"🔴 Connecting to Redis...","timestamp":"2025-06-20 17:59:40:5940"}
{"level":"info","message":"🔴 Connecting to Redis...","timestamp":"2025-06-20 17:59:40:5940"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 17:59:41:5941"}
{"level":"info","message":"✅ Redis client ready","timestamp":"2025-06-20 17:59:41:5941"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-20 17:59:41:5941"}
{"level":"info","message":"✅ Redis connection completed","timestamp":"2025-06-20 17:59:41:5941"}
{"level":"info","message":"📋 Setting up API routes...","timestamp":"2025-06-20 17:59:41:5941"}
{"level":"info","message":"📧 Email service initialized (verification disabled for testing)","timestamp":"2025-06-20 17:59:42:5942"}
{"level":"info","message":"Email templates initialized successfully","timestamp":"2025-06-20 17:59:43:5943"}
{"level":"info","message":"✅ API routes configured","timestamp":"2025-06-20 17:59:43:5943"}
{"level":"info","message":"⚙️ Initializing services...","timestamp":"2025-06-20 17:59:43:5943"}
{"level":"info","message":"🔄 Connecting cache service...","timestamp":"2025-06-20 17:59:43:5943"}
{"level":"info","message":"Redis cache client connecting...","timestamp":"2025-06-20 17:59:43:5943"}
{"level":"info","message":"Redis cache client connected and ready","timestamp":"2025-06-20 17:59:43:5943"}
{"level":"info","message":"✅ Cache service connected","timestamp":"2025-06-20 17:59:43:5943"}
{"level":"info","message":"🔄 Connecting session service...","timestamp":"2025-06-20 17:59:43:5943"}
{"level":"info","message":"Redis session client connecting...","timestamp":"2025-06-20 17:59:43:5943"}
{"level":"info","message":"Redis session client connected and ready","timestamp":"2025-06-20 17:59:44:5944"}
{"level":"info","message":"✅ Session service connected","timestamp":"2025-06-20 17:59:44:5944"}
{"level":"info","message":"🔄 Connecting token service...","timestamp":"2025-06-20 17:59:44:5944"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 17:59:44:5944"}
{"level":"info","message":"Redis token client connected and ready","timestamp":"2025-06-20 17:59:44:5944"}
{"level":"info","message":"✅ Token service connected","timestamp":"2025-06-20 17:59:44:5944"}
{"level":"info","message":"All services initialized successfully","timestamp":"2025-06-20 17:59:44:5944"}
{"level":"info","message":"✅ Services initialization completed","timestamp":"2025-06-20 17:59:44:5944"}
{"level":"info","message":"🔐 Setting up session middleware...","timestamp":"2025-06-20 17:59:44:5944"}
{"level":"info","message":"🛡️ Setting up rate limiting...","timestamp":"2025-06-20 17:59:44:5944"}
{"level":"info","message":"💬 Initializing Socket.IO service...","timestamp":"2025-06-20 17:59:44:5944"}
{"level":"info","message":"✅ Socket.IO service initialized","timestamp":"2025-06-20 17:59:44:5944"}
{"level":"info","message":"🚀 LajoSpaces Backend Server running on port 5000","timestamp":"2025-06-20 17:59:44:5944"}
{"level":"info","message":"🌍 Environment: development","timestamp":"2025-06-20 17:59:44:5944"}
{"level":"info","message":"📊 Health check: http://localhost:5000/health","timestamp":"2025-06-20 17:59:44:5944"}
{"level":"info","message":"🔗 API Base URL: http://localhost:5000/api","timestamp":"2025-06-20 17:59:44:5944"}
{"level":"info","message":"💬 Socket.IO enabled for real-time messaging","timestamp":"2025-06-20 17:59:44:5944"}
{"level":"info","message":"::1 - - [20/Jun/2025:17:00:32 +0000] \"GET /health HTTP/1.1\" 200 221 \"-\" \"curl/8.6.0\"","timestamp":"2025-06-20 18:00:32:032"}
{"error":{"message":"Route /api/auth/health not found on this server","name":"Error","stack":"Error: Route /api/auth/health not found on this server\n    at notFoundHandler (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\middleware\\notFoundHandler.ts:9:17)\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts:180:3\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)"},"level":"error","message":"Error 404: Route /api/auth/health not found on this server","request":{"ip":"::1","method":"GET","url":"/api/auth/health","userAgent":"curl/8.6.0"},"timestamp":"2025-06-20 18:00:37:037","user":null}
{"level":"info","message":"::1 - - [20/Jun/2025:17:00:37 +0000] \"GET /api/auth/health HTTP/1.1\" 404 344 \"-\" \"curl/8.6.0\"","timestamp":"2025-06-20 18:00:37:037"}
{"endpoint":"/api/auth/health","eventType":"data_viewed","ipAddress":"::1","level":"info","message":"Audit event logged","success":false,"timestamp":"2025-06-20 18:00:37:037"}
{"error":{"message":"Route /api/auth/health not found on this server","name":"Error","stack":"Error: Route /api/auth/health not found on this server\n    at notFoundHandler (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\middleware\\notFoundHandler.ts:9:17)\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts:180:3\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)"},"level":"error","message":"Error 404: Route /api/auth/health not found on this server","request":{"ip":"::1","method":"GET","url":"/api/auth/health","userAgent":"curl/8.6.0"},"timestamp":"2025-06-20 18:00:51:051","user":null}
{"level":"info","message":"::1 - - [20/Jun/2025:17:00:51 +0000] \"GET /api/auth/health HTTP/1.1\" 404 344 \"-\" \"curl/8.6.0\"","timestamp":"2025-06-20 18:00:51:051"}
{"endpoint":"/api/auth/health","eventType":"data_viewed","ipAddress":"::1","level":"info","message":"Audit event logged","success":false,"timestamp":"2025-06-20 18:00:51:051"}
{"level":"info","message":"::1 - - [20/Jun/2025:17:00:59 +0000] \"GET /api HTTP/1.1\" 200 502 \"-\" \"curl/8.6.0\"","timestamp":"2025-06-20 18:00:59:059"}
{"error":{"message":"Route /api/auth/health not found on this server","name":"Error","stack":"Error: Route /api/auth/health not found on this server\n    at notFoundHandler (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\middleware\\notFoundHandler.ts:9:17)\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts:180:3\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)"},"level":"error","message":"Error 404: Route /api/auth/health not found on this server","request":{"ip":"::1","method":"GET","url":"/api/auth/health","userAgent":"curl/8.6.0"},"timestamp":"2025-06-20 18:01:08:18","user":null}
{"level":"info","message":"::1 - - [20/Jun/2025:17:01:08 +0000] \"GET /api/auth/health HTTP/1.1\" 404 344 \"-\" \"curl/8.6.0\"","timestamp":"2025-06-20 18:01:08:18"}
{"endpoint":"/api/auth/health","eventType":"data_viewed","ipAddress":"::1","level":"info","message":"Audit event logged","success":false,"timestamp":"2025-06-20 18:01:09:19"}
{"error":{"message":"Route /api/users/health not found on this server","name":"Error","stack":"Error: Route /api/users/health not found on this server\n    at notFoundHandler (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\middleware\\notFoundHandler.ts:9:17)\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts:180:3\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)"},"level":"error","message":"Error 404: Route /api/users/health not found on this server","request":{"ip":"::1","method":"GET","url":"/api/users/health","userAgent":"curl/8.6.0"},"timestamp":"2025-06-20 18:01:28:128","user":null}
{"level":"info","message":"::1 - - [20/Jun/2025:17:01:28 +0000] \"GET /api/users/health HTTP/1.1\" 404 347 \"-\" \"curl/8.6.0\"","timestamp":"2025-06-20 18:01:28:128"}
{"endpoint":"/api/users/health","eventType":"data_viewed","ipAddress":"::1","level":"info","message":"Audit event logged","success":false,"timestamp":"2025-06-20 18:01:28:128"}
{"level":"info","message":"🚀 Starting server initialization...","timestamp":"2025-06-20 18:02:51:251"}
{"level":"info","message":"🚀 Starting LajoSpaces Backend Server...","timestamp":"2025-06-20 18:02:51:251"}
{"level":"info","message":"📊 Connecting to MongoDB...","timestamp":"2025-06-20 18:02:51:251"}
{"level":"info","message":"📊 Connecting to MongoDB...","timestamp":"2025-06-20 18:02:51:251"}
{"level":"info","message":"📊 Mongoose connected to MongoDB","timestamp":"2025-06-20 18:02:54:254"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-20 18:02:54:254"}
{"level":"info","message":"📊 Connected to database: lajospaces","timestamp":"2025-06-20 18:02:54:254"}
{"level":"info","message":"✅ MongoDB connection completed","timestamp":"2025-06-20 18:02:54:254"}
{"level":"info","message":"🔴 Connecting to Redis...","timestamp":"2025-06-20 18:02:54:254"}
{"level":"info","message":"🔴 Connecting to Redis...","timestamp":"2025-06-20 18:02:54:254"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:02:54:254"}
{"level":"info","message":"✅ Redis client ready","timestamp":"2025-06-20 18:02:54:254"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-20 18:02:54:254"}
{"level":"info","message":"✅ Redis connection completed","timestamp":"2025-06-20 18:02:54:254"}
{"level":"info","message":"⚙️ Initializing services...","timestamp":"2025-06-20 18:02:54:254"}
{"level":"info","message":"🔄 Connecting cache service...","timestamp":"2025-06-20 18:02:54:254"}
{"level":"info","message":"Redis cache client connecting...","timestamp":"2025-06-20 18:02:55:255"}
{"level":"info","message":"Redis cache client connected and ready","timestamp":"2025-06-20 18:02:55:255"}
{"level":"info","message":"✅ Cache service connected","timestamp":"2025-06-20 18:02:55:255"}
{"level":"info","message":"🔄 Connecting session service...","timestamp":"2025-06-20 18:02:55:255"}
{"level":"info","message":"Redis session client connecting...","timestamp":"2025-06-20 18:02:56:256"}
{"level":"info","message":"Redis session client connected and ready","timestamp":"2025-06-20 18:02:56:256"}
{"level":"info","message":"✅ Session service connected","timestamp":"2025-06-20 18:02:56:256"}
{"level":"info","message":"🔄 Connecting token service...","timestamp":"2025-06-20 18:02:56:256"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:02:57:257"}
{"level":"info","message":"Redis token client connected and ready","timestamp":"2025-06-20 18:02:57:257"}
{"level":"info","message":"✅ Token service connected","timestamp":"2025-06-20 18:02:57:257"}
{"level":"info","message":"All services initialized successfully","timestamp":"2025-06-20 18:02:57:257"}
{"level":"info","message":"✅ Services initialization completed","timestamp":"2025-06-20 18:02:57:257"}
{"level":"info","message":"🔐 Setting up session middleware...","timestamp":"2025-06-20 18:02:57:257"}
{"level":"info","message":"🛡️ Setting up rate limiting...","timestamp":"2025-06-20 18:02:57:257"}
{"level":"info","message":"📋 Setting up API routes...","timestamp":"2025-06-20 18:02:57:257"}
{"level":"info","message":"📧 Email service initialized (verification disabled for testing)","timestamp":"2025-06-20 18:02:57:257"}
{"level":"info","message":"Email templates initialized successfully","timestamp":"2025-06-20 18:02:58:258"}
{"level":"info","message":"✅ API routes configured","timestamp":"2025-06-20 18:02:58:258"}
{"level":"info","message":"💬 Initializing Socket.IO service...","timestamp":"2025-06-20 18:02:58:258"}
{"level":"info","message":"✅ Socket.IO service initialized","timestamp":"2025-06-20 18:02:58:258"}
{"level":"info","message":"🚀 LajoSpaces Backend Server running on port 5000","timestamp":"2025-06-20 18:02:58:258"}
{"level":"info","message":"🌍 Environment: development","timestamp":"2025-06-20 18:02:58:258"}
{"level":"info","message":"📊 Health check: http://localhost:5000/health","timestamp":"2025-06-20 18:02:58:258"}
{"level":"info","message":"🔗 API Base URL: http://localhost:5000/api","timestamp":"2025-06-20 18:02:58:258"}
{"level":"info","message":"💬 Socket.IO enabled for real-time messaging","timestamp":"2025-06-20 18:02:58:258"}
{"error":{"message":"Route /api/auth/health not found on this server","name":"Error","stack":"Error: Route /api/auth/health not found on this server\n    at notFoundHandler (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\middleware\\notFoundHandler.ts:9:17)\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts:180:3\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)"},"level":"error","message":"Error 404: Route /api/auth/health not found on this server","request":{"ip":"::1","method":"GET","url":"/api/auth/health","userAgent":"curl/8.6.0"},"timestamp":"2025-06-20 18:03:45:345","user":null}
{"level":"info","message":"::1 - - [20/Jun/2025:17:03:45 +0000] \"GET /api/auth/health HTTP/1.1\" 404 344 \"-\" \"curl/8.6.0\"","timestamp":"2025-06-20 18:03:45:345"}
{"endpoint":"/api/auth/health","eventType":"data_viewed","ipAddress":"::1","level":"info","message":"Audit event logged","success":false,"timestamp":"2025-06-20 18:03:45:345"}
{"level":"info","message":"🚀 Starting server initialization...","timestamp":"2025-06-20 18:04:43:443"}
{"level":"info","message":"🚀 Starting LajoSpaces Backend Server...","timestamp":"2025-06-20 18:04:43:443"}
{"level":"info","message":"📊 Connecting to MongoDB...","timestamp":"2025-06-20 18:04:43:443"}
{"level":"info","message":"📊 Connecting to MongoDB...","timestamp":"2025-06-20 18:04:43:443"}
{"level":"info","message":"📊 Mongoose connected to MongoDB","timestamp":"2025-06-20 18:04:46:446"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-20 18:04:46:446"}
{"level":"info","message":"📊 Connected to database: lajospaces","timestamp":"2025-06-20 18:04:46:446"}
{"level":"info","message":"✅ MongoDB connection completed","timestamp":"2025-06-20 18:04:46:446"}
{"level":"info","message":"🔴 Connecting to Redis...","timestamp":"2025-06-20 18:04:46:446"}
{"level":"info","message":"🔴 Connecting to Redis...","timestamp":"2025-06-20 18:04:46:446"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:04:47:447"}
{"level":"info","message":"✅ Redis client ready","timestamp":"2025-06-20 18:04:47:447"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-20 18:04:47:447"}
{"level":"info","message":"✅ Redis connection completed","timestamp":"2025-06-20 18:04:47:447"}
{"level":"info","message":"⚙️ Initializing services...","timestamp":"2025-06-20 18:04:47:447"}
{"level":"info","message":"🔄 Connecting cache service...","timestamp":"2025-06-20 18:04:47:447"}
{"level":"info","message":"Redis cache client connecting...","timestamp":"2025-06-20 18:04:47:447"}
{"level":"info","message":"Redis cache client connected and ready","timestamp":"2025-06-20 18:04:48:448"}
{"level":"info","message":"✅ Cache service connected","timestamp":"2025-06-20 18:04:48:448"}
{"level":"info","message":"🔄 Connecting session service...","timestamp":"2025-06-20 18:04:48:448"}
{"level":"info","message":"Redis session client connecting...","timestamp":"2025-06-20 18:04:48:448"}
{"level":"info","message":"Redis session client connected and ready","timestamp":"2025-06-20 18:04:48:448"}
{"level":"info","message":"✅ Session service connected","timestamp":"2025-06-20 18:04:48:448"}
{"level":"info","message":"🔄 Connecting token service...","timestamp":"2025-06-20 18:04:48:448"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:04:48:448"}
{"level":"info","message":"Redis token client connected and ready","timestamp":"2025-06-20 18:04:48:448"}
{"level":"info","message":"✅ Token service connected","timestamp":"2025-06-20 18:04:48:448"}
{"level":"info","message":"All services initialized successfully","timestamp":"2025-06-20 18:04:48:448"}
{"level":"info","message":"✅ Services initialization completed","timestamp":"2025-06-20 18:04:48:448"}
{"level":"info","message":"🔐 Setting up session middleware...","timestamp":"2025-06-20 18:04:48:448"}
{"level":"info","message":"🛡️ Setting up rate limiting...","timestamp":"2025-06-20 18:04:48:448"}
{"level":"info","message":"📋 Setting up API routes...","timestamp":"2025-06-20 18:04:48:448"}
{"level":"info","message":"📧 Email service initialized (verification disabled for testing)","timestamp":"2025-06-20 18:04:49:449"}
{"level":"info","message":"Email templates initialized successfully","timestamp":"2025-06-20 18:04:49:449"}
{"level":"info","message":"✅ API routes configured","timestamp":"2025-06-20 18:04:50:450"}
{"level":"info","message":"💬 Initializing Socket.IO service...","timestamp":"2025-06-20 18:04:50:450"}
{"level":"info","message":"✅ Socket.IO service initialized","timestamp":"2025-06-20 18:04:50:450"}
{"level":"info","message":"🚀 LajoSpaces Backend Server running on port 5000","timestamp":"2025-06-20 18:04:50:450"}
{"level":"info","message":"🌍 Environment: development","timestamp":"2025-06-20 18:04:50:450"}
{"level":"info","message":"📊 Health check: http://localhost:5000/health","timestamp":"2025-06-20 18:04:50:450"}
{"level":"info","message":"🔗 API Base URL: http://localhost:5000/api","timestamp":"2025-06-20 18:04:50:450"}
{"level":"info","message":"💬 Socket.IO enabled for real-time messaging","timestamp":"2025-06-20 18:04:50:450"}
{"error":{"message":"Route /api/auth/health not found on this server","name":"Error","stack":"Error: Route /api/auth/health not found on this server\n    at notFoundHandler (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\middleware\\notFoundHandler.ts:9:17)\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts:180:3\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)"},"level":"error","message":"Error 404: Route /api/auth/health not found on this server","request":{"ip":"::1","method":"GET","url":"/api/auth/health","userAgent":"curl/8.6.0"},"timestamp":"2025-06-20 18:05:22:522","user":null}
{"level":"info","message":"::1 - - [20/Jun/2025:17:05:22 +0000] \"GET /api/auth/health HTTP/1.1\" 404 344 \"-\" \"curl/8.6.0\"","timestamp":"2025-06-20 18:05:22:522"}
{"endpoint":"/api/auth/health","eventType":"data_viewed","ipAddress":"::1","level":"info","message":"Audit event logged","success":false,"timestamp":"2025-06-20 18:05:22:522"}
{"level":"info","message":"🚀 Starting server initialization...","timestamp":"2025-06-20 18:06:00:60"}
{"level":"info","message":"🚀 Starting LajoSpaces Backend Server...","timestamp":"2025-06-20 18:06:00:60"}
{"level":"info","message":"📊 Connecting to MongoDB...","timestamp":"2025-06-20 18:06:00:60"}
{"level":"info","message":"📊 Connecting to MongoDB...","timestamp":"2025-06-20 18:06:00:60"}
{"level":"info","message":"📊 Mongoose connected to MongoDB","timestamp":"2025-06-20 18:06:01:61"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-20 18:06:01:61"}
{"level":"info","message":"📊 Connected to database: lajospaces","timestamp":"2025-06-20 18:06:01:61"}
{"level":"info","message":"✅ MongoDB connection completed","timestamp":"2025-06-20 18:06:01:61"}
{"level":"info","message":"🔴 Connecting to Redis...","timestamp":"2025-06-20 18:06:01:61"}
{"level":"info","message":"🔴 Connecting to Redis...","timestamp":"2025-06-20 18:06:01:61"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:06:02:62"}
{"level":"info","message":"✅ Redis client ready","timestamp":"2025-06-20 18:06:02:62"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-20 18:06:02:62"}
{"level":"info","message":"✅ Redis connection completed","timestamp":"2025-06-20 18:06:02:62"}
{"level":"info","message":"⚙️ Initializing services...","timestamp":"2025-06-20 18:06:02:62"}
{"level":"info","message":"🔄 Connecting cache service...","timestamp":"2025-06-20 18:06:02:62"}
{"level":"info","message":"Redis cache client connecting...","timestamp":"2025-06-20 18:06:02:62"}
{"level":"info","message":"Redis cache client connected and ready","timestamp":"2025-06-20 18:06:03:63"}
{"level":"info","message":"✅ Cache service connected","timestamp":"2025-06-20 18:06:03:63"}
{"level":"info","message":"🔄 Connecting session service...","timestamp":"2025-06-20 18:06:03:63"}
{"level":"info","message":"Redis session client connecting...","timestamp":"2025-06-20 18:06:03:63"}
{"level":"info","message":"Redis session client connected and ready","timestamp":"2025-06-20 18:06:03:63"}
{"level":"info","message":"✅ Session service connected","timestamp":"2025-06-20 18:06:03:63"}
{"level":"info","message":"🔄 Connecting token service...","timestamp":"2025-06-20 18:06:03:63"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:06:03:63"}
{"level":"info","message":"Redis token client connected and ready","timestamp":"2025-06-20 18:06:03:63"}
{"level":"info","message":"✅ Token service connected","timestamp":"2025-06-20 18:06:03:63"}
{"level":"info","message":"All services initialized successfully","timestamp":"2025-06-20 18:06:03:63"}
{"level":"info","message":"✅ Services initialization completed","timestamp":"2025-06-20 18:06:03:63"}
{"level":"info","message":"🔐 Setting up session middleware...","timestamp":"2025-06-20 18:06:03:63"}
{"level":"info","message":"🛡️ Setting up rate limiting...","timestamp":"2025-06-20 18:06:03:63"}
{"level":"info","message":"📋 Setting up API routes...","timestamp":"2025-06-20 18:06:03:63"}
{"level":"info","message":"📧 Email service initialized (verification disabled for testing)","timestamp":"2025-06-20 18:06:04:64"}
{"level":"info","message":"Email templates initialized successfully","timestamp":"2025-06-20 18:06:05:65"}
{"level":"info","message":"🔗 Mounting auth routes...","timestamp":"2025-06-20 18:06:05:65"}
{"level":"info","message":"🔗 Mounting user routes...","timestamp":"2025-06-20 18:06:05:65"}
{"level":"info","message":"🔗 Mounting profile routes...","timestamp":"2025-06-20 18:06:05:65"}
{"level":"info","message":"🔗 Mounting photo routes...","timestamp":"2025-06-20 18:06:05:65"}
{"level":"info","message":"🔗 Mounting upload routes...","timestamp":"2025-06-20 18:06:05:65"}
{"level":"info","message":"🔗 Mounting search routes...","timestamp":"2025-06-20 18:06:05:65"}
{"level":"info","message":"🔗 Mounting property routes...","timestamp":"2025-06-20 18:06:05:65"}
{"level":"info","message":"🔗 All routes mounted successfully","timestamp":"2025-06-20 18:06:05:65"}
{"level":"info","message":"✅ API routes configured","timestamp":"2025-06-20 18:06:05:65"}
{"level":"info","message":"💬 Initializing Socket.IO service...","timestamp":"2025-06-20 18:06:05:65"}
{"level":"info","message":"✅ Socket.IO service initialized","timestamp":"2025-06-20 18:06:05:65"}
{"level":"info","message":"🚀 LajoSpaces Backend Server running on port 5000","timestamp":"2025-06-20 18:06:05:65"}
{"level":"info","message":"🌍 Environment: development","timestamp":"2025-06-20 18:06:05:65"}
{"level":"info","message":"📊 Health check: http://localhost:5000/health","timestamp":"2025-06-20 18:06:05:65"}
{"level":"info","message":"🔗 API Base URL: http://localhost:5000/api","timestamp":"2025-06-20 18:06:05:65"}
{"level":"info","message":"💬 Socket.IO enabled for real-time messaging","timestamp":"2025-06-20 18:06:05:65"}
{"error":{"message":"Route /api/auth/health not found on this server","name":"Error","stack":"Error: Route /api/auth/health not found on this server\n    at notFoundHandler (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\middleware\\notFoundHandler.ts:9:17)\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts:188:3\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)"},"level":"error","message":"Error 404: Route /api/auth/health not found on this server","request":{"ip":"::1","method":"GET","url":"/api/auth/health","userAgent":"curl/8.6.0"},"timestamp":"2025-06-20 18:06:40:640","user":null}
{"level":"info","message":"::1 - - [20/Jun/2025:17:06:40 +0000] \"GET /api/auth/health HTTP/1.1\" 404 344 \"-\" \"curl/8.6.0\"","timestamp":"2025-06-20 18:06:40:640"}
{"endpoint":"/api/auth/health","eventType":"data_viewed","ipAddress":"::1","level":"info","message":"Audit event logged","success":false,"timestamp":"2025-06-20 18:06:40:640"}
{"level":"info","message":"🚀 Starting server initialization...","timestamp":"2025-06-20 18:08:01:81"}
{"level":"info","message":"🚀 Starting LajoSpaces Backend Server...","timestamp":"2025-06-20 18:08:01:81"}
{"level":"info","message":"📊 Connecting to MongoDB...","timestamp":"2025-06-20 18:08:01:81"}
{"level":"info","message":"📊 Connecting to MongoDB...","timestamp":"2025-06-20 18:08:01:81"}
{"level":"info","message":"📊 Mongoose connected to MongoDB","timestamp":"2025-06-20 18:08:03:83"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-20 18:08:03:83"}
{"level":"info","message":"📊 Connected to database: lajospaces","timestamp":"2025-06-20 18:08:03:83"}
{"level":"info","message":"✅ MongoDB connection completed","timestamp":"2025-06-20 18:08:03:83"}
{"level":"info","message":"🔴 Connecting to Redis...","timestamp":"2025-06-20 18:08:03:83"}
{"level":"info","message":"🔴 Connecting to Redis...","timestamp":"2025-06-20 18:08:03:83"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:08:04:84"}
{"level":"info","message":"✅ Redis client ready","timestamp":"2025-06-20 18:08:04:84"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-20 18:08:04:84"}
{"level":"info","message":"✅ Redis connection completed","timestamp":"2025-06-20 18:08:04:84"}
{"level":"info","message":"⚙️ Initializing services...","timestamp":"2025-06-20 18:08:04:84"}
{"level":"info","message":"🔄 Connecting cache service...","timestamp":"2025-06-20 18:08:04:84"}
{"level":"info","message":"Redis cache client connecting...","timestamp":"2025-06-20 18:08:04:84"}
{"level":"info","message":"Redis cache client connected and ready","timestamp":"2025-06-20 18:08:04:84"}
{"level":"info","message":"✅ Cache service connected","timestamp":"2025-06-20 18:08:04:84"}
{"level":"info","message":"🔄 Connecting session service...","timestamp":"2025-06-20 18:08:04:84"}
{"level":"info","message":"Redis session client connecting...","timestamp":"2025-06-20 18:08:05:85"}
{"level":"info","message":"Redis session client connected and ready","timestamp":"2025-06-20 18:08:05:85"}
{"level":"info","message":"✅ Session service connected","timestamp":"2025-06-20 18:08:05:85"}
{"level":"info","message":"🔄 Connecting token service...","timestamp":"2025-06-20 18:08:05:85"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:08:05:85"}
{"level":"info","message":"Redis token client connected and ready","timestamp":"2025-06-20 18:08:05:85"}
{"level":"info","message":"✅ Token service connected","timestamp":"2025-06-20 18:08:05:85"}
{"level":"info","message":"All services initialized successfully","timestamp":"2025-06-20 18:08:05:85"}
{"level":"info","message":"✅ Services initialization completed","timestamp":"2025-06-20 18:08:05:85"}
{"level":"info","message":"🔐 Setting up session middleware...","timestamp":"2025-06-20 18:08:05:85"}
{"level":"info","message":"🛡️ Setting up rate limiting...","timestamp":"2025-06-20 18:08:05:85"}
{"level":"info","message":"📋 Setting up API routes...","timestamp":"2025-06-20 18:08:05:85"}
{"level":"info","message":"📧 Email service initialized (verification disabled for testing)","timestamp":"2025-06-20 18:08:06:86"}
{"level":"info","message":"Email templates initialized successfully","timestamp":"2025-06-20 18:08:06:86"}
{"level":"info","message":"🔗 Mounting auth routes...","timestamp":"2025-06-20 18:08:06:86"}
{"level":"info","message":"🔗 Mounting user routes...","timestamp":"2025-06-20 18:08:06:86"}
{"level":"info","message":"🔗 Mounting profile routes...","timestamp":"2025-06-20 18:08:06:86"}
{"level":"info","message":"🔗 Mounting photo routes...","timestamp":"2025-06-20 18:08:06:86"}
{"level":"info","message":"🔗 Mounting upload routes...","timestamp":"2025-06-20 18:08:06:86"}
{"level":"info","message":"🔗 Mounting search routes...","timestamp":"2025-06-20 18:08:06:86"}
{"level":"info","message":"🔗 Mounting property routes...","timestamp":"2025-06-20 18:08:06:86"}
{"level":"info","message":"🔗 All routes mounted successfully","timestamp":"2025-06-20 18:08:06:86"}
{"level":"info","message":"✅ API routes configured","timestamp":"2025-06-20 18:08:06:86"}
{"level":"info","message":"💬 Initializing Socket.IO service...","timestamp":"2025-06-20 18:08:06:86"}
{"level":"info","message":"✅ Socket.IO service initialized","timestamp":"2025-06-20 18:08:07:87"}
{"error":{"message":"Route /api/properties/health not found on this server","name":"Error","stack":"Error: Route /api/properties/health not found on this server\n    at notFoundHandler (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\middleware\\notFoundHandler.ts:9:17)\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts:188:3\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)"},"level":"error","message":"Error 404: Route /api/properties/health not found on this server","request":{"ip":"::1","method":"GET","url":"/api/properties/health","userAgent":"curl/8.6.0"},"timestamp":"2025-06-20 18:09:58:958","user":null}
{"level":"info","message":"::1 - - [20/Jun/2025:17:09:58 +0000] \"GET /api/properties/health HTTP/1.1\" 404 362 \"-\" \"curl/8.6.0\"","timestamp":"2025-06-20 18:09:58:958"}
{"endpoint":"/api/properties/health","eventType":"data_viewed","ipAddress":"::1","level":"info","message":"Audit event logged","success":false,"timestamp":"2025-06-20 18:09:58:958"}
{"error":{"message":"Route /api/properties/health not found on this server","name":"Error","stack":"Error: Route /api/properties/health not found on this server\n    at notFoundHandler (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\middleware\\notFoundHandler.ts:9:17)\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts:188:3\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)"},"level":"error","message":"Error 404: Route /api/properties/health not found on this server","request":{"ip":"::1","method":"GET","url":"/api/properties/health","userAgent":"curl/8.6.0"},"timestamp":"2025-06-20 18:10:12:1012","user":null}
{"level":"info","message":"::1 - - [20/Jun/2025:17:10:12 +0000] \"GET /api/properties/health HTTP/1.1\" 404 362 \"-\" \"curl/8.6.0\"","timestamp":"2025-06-20 18:10:12:1012"}
{"endpoint":"/api/properties/health","eventType":"data_viewed","ipAddress":"::1","level":"info","message":"Audit event logged","success":false,"timestamp":"2025-06-20 18:10:12:1012"}
{"error":{"message":"Route /api/properties/health not found on this server","name":"Error","stack":"Error: Route /api/properties/health not found on this server\n    at notFoundHandler (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\middleware\\notFoundHandler.ts:9:17)\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts:188:3\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)"},"level":"error","message":"Error 404: Route /api/properties/health not found on this server","request":{"ip":"::1","method":"GET","url":"/api/properties/health","userAgent":"curl/8.6.0"},"timestamp":"2025-06-20 18:13:39:1339","user":null}
{"level":"info","message":"::1 - - [20/Jun/2025:17:13:39 +0000] \"GET /api/properties/health HTTP/1.1\" 404 362 \"-\" \"curl/8.6.0\"","timestamp":"2025-06-20 18:13:39:1339"}
{"endpoint":"/api/properties/health","eventType":"data_viewed","ipAddress":"::1","level":"info","message":"Audit event logged","success":false,"timestamp":"2025-06-20 18:13:39:1339"}
{"error":{"message":"Route /api/properties?limit=1 not found on this server","name":"Error","stack":"Error: Route /api/properties?limit=1 not found on this server\n    at notFoundHandler (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\middleware\\notFoundHandler.ts:9:17)\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts:188:3\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)"},"level":"error","message":"Error 404: Route /api/properties?limit=1 not found on this server","request":{"ip":"::1","method":"GET","url":"/api/properties?limit=1","userAgent":"curl/8.6.0"},"timestamp":"2025-06-20 18:13:58:1358","user":null}
{"level":"info","message":"::1 - - [20/Jun/2025:17:13:58 +0000] \"GET /api/properties?limit=1 HTTP/1.1\" 404 357 \"-\" \"curl/8.6.0\"","timestamp":"2025-06-20 18:13:58:1358"}
{"endpoint":"/api/properties?limit=1","eventType":"data_viewed","ipAddress":"::1","level":"info","message":"Audit event logged","success":false,"timestamp":"2025-06-20 18:13:58:1358"}
{"error":{"message":"Route /api/properties?limit=1 not found on this server","name":"Error","stack":"Error: Route /api/properties?limit=1 not found on this server\n    at notFoundHandler (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\middleware\\notFoundHandler.ts:9:17)\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts:188:3\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)"},"level":"error","message":"Error 404: Route /api/properties?limit=1 not found on this server","request":{"ip":"::1","method":"GET","url":"/api/properties?limit=1","userAgent":"curl/8.6.0"},"timestamp":"2025-06-20 18:14:41:1441","user":null}
{"level":"info","message":"::1 - - [20/Jun/2025:17:14:41 +0000] \"GET /api/properties?limit=1 HTTP/1.1\" 404 357 \"-\" \"curl/8.6.0\"","timestamp":"2025-06-20 18:14:41:1441"}
{"endpoint":"/api/properties?limit=1","eventType":"data_viewed","ipAddress":"::1","level":"info","message":"Audit event logged","success":false,"timestamp":"2025-06-20 18:14:41:1441"}
{"level":"info","message":"🚀 Starting server initialization...","timestamp":"2025-06-20 18:33:56:3356"}
{"level":"info","message":"🚀 Starting LajoSpaces Backend Server...","timestamp":"2025-06-20 18:33:56:3356"}
{"level":"info","message":"📊 Connecting to MongoDB...","timestamp":"2025-06-20 18:33:56:3356"}
{"level":"info","message":"📊 Connecting to MongoDB...","timestamp":"2025-06-20 18:33:56:3356"}
{"level":"info","message":"📊 Mongoose connected to MongoDB","timestamp":"2025-06-20 18:33:58:3358"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-20 18:33:58:3358"}
{"level":"info","message":"📊 Connected to database: lajospaces","timestamp":"2025-06-20 18:33:58:3358"}
{"level":"info","message":"✅ MongoDB connection completed","timestamp":"2025-06-20 18:33:58:3358"}
{"level":"info","message":"🔴 Connecting to Redis...","timestamp":"2025-06-20 18:33:58:3358"}
{"level":"info","message":"🔴 Connecting to Redis...","timestamp":"2025-06-20 18:33:58:3358"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:33:59:3359"}
{"level":"info","message":"✅ Redis client ready","timestamp":"2025-06-20 18:33:59:3359"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-20 18:33:59:3359"}
{"level":"info","message":"✅ Redis connection completed","timestamp":"2025-06-20 18:33:59:3359"}
{"level":"info","message":"⚙️ Initializing services...","timestamp":"2025-06-20 18:33:59:3359"}
{"level":"info","message":"🔄 Connecting cache service...","timestamp":"2025-06-20 18:33:59:3359"}
{"level":"info","message":"Redis cache client connecting...","timestamp":"2025-06-20 18:34:00:340"}
{"level":"info","message":"Redis cache client connected and ready","timestamp":"2025-06-20 18:34:00:340"}
{"level":"info","message":"✅ Cache service connected","timestamp":"2025-06-20 18:34:00:340"}
{"level":"info","message":"🔄 Connecting session service...","timestamp":"2025-06-20 18:34:00:340"}
{"level":"info","message":"Redis session client connecting...","timestamp":"2025-06-20 18:34:00:340"}
{"level":"info","message":"Redis session client connected and ready","timestamp":"2025-06-20 18:34:00:340"}
{"level":"info","message":"✅ Session service connected","timestamp":"2025-06-20 18:34:00:340"}
{"level":"info","message":"🔄 Connecting token service...","timestamp":"2025-06-20 18:34:00:340"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:34:00:340"}
{"level":"info","message":"Redis token client connected and ready","timestamp":"2025-06-20 18:34:01:341"}
{"level":"info","message":"✅ Token service connected","timestamp":"2025-06-20 18:34:01:341"}
{"level":"info","message":"All services initialized successfully","timestamp":"2025-06-20 18:34:01:341"}
{"level":"info","message":"✅ Services initialization completed","timestamp":"2025-06-20 18:34:01:341"}
{"level":"info","message":"🔐 Setting up session middleware...","timestamp":"2025-06-20 18:34:01:341"}
{"level":"info","message":"🛡️ Setting up rate limiting...","timestamp":"2025-06-20 18:34:01:341"}
{"level":"info","message":"📋 Setting up API routes...","timestamp":"2025-06-20 18:34:01:341"}
{"level":"info","message":"📧 Email service initialized (verification disabled for testing)","timestamp":"2025-06-20 18:34:01:341"}
{"level":"info","message":"Email templates initialized successfully","timestamp":"2025-06-20 18:34:02:342"}
{"level":"info","message":"🔗 Mounting auth routes...","timestamp":"2025-06-20 18:34:02:342"}
{"level":"info","message":"🔗 Mounting user routes...","timestamp":"2025-06-20 18:34:02:342"}
{"level":"info","message":"🔗 Mounting profile routes...","timestamp":"2025-06-20 18:34:02:342"}
{"level":"info","message":"🔗 Mounting photo routes...","timestamp":"2025-06-20 18:34:02:342"}
{"level":"info","message":"🔗 Mounting upload routes...","timestamp":"2025-06-20 18:34:02:342"}
{"level":"info","message":"🔗 Mounting search routes...","timestamp":"2025-06-20 18:34:02:342"}
{"level":"info","message":"🔗 Mounting property routes...","timestamp":"2025-06-20 18:34:02:342"}
{"level":"info","message":"🔗 All routes mounted successfully","timestamp":"2025-06-20 18:34:02:342"}
{"level":"info","message":"✅ API routes configured","timestamp":"2025-06-20 18:34:02:342"}
{"level":"info","message":"💬 Initializing Socket.IO service...","timestamp":"2025-06-20 18:34:02:342"}
{"level":"info","message":"✅ Socket.IO service initialized","timestamp":"2025-06-20 18:34:02:342"}
{"level":"info","message":"🚀 Starting server initialization...","timestamp":"2025-06-20 18:36:51:3651"}
{"level":"info","message":"🚀 Starting LajoSpaces Backend Server...","timestamp":"2025-06-20 18:36:51:3651"}
{"level":"info","message":"📊 Connecting to MongoDB...","timestamp":"2025-06-20 18:36:51:3651"}
{"level":"info","message":"📊 Connecting to MongoDB...","timestamp":"2025-06-20 18:36:51:3651"}
{"level":"info","message":"📊 Mongoose connected to MongoDB","timestamp":"2025-06-20 18:36:53:3653"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-20 18:36:53:3653"}
{"level":"info","message":"📊 Connected to database: lajospaces","timestamp":"2025-06-20 18:36:53:3653"}
{"level":"info","message":"✅ MongoDB connection completed","timestamp":"2025-06-20 18:36:53:3653"}
{"level":"info","message":"🔴 Connecting to Redis...","timestamp":"2025-06-20 18:36:53:3653"}
{"level":"info","message":"🔴 Connecting to Redis...","timestamp":"2025-06-20 18:36:53:3653"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:36:54:3654"}
{"level":"info","message":"✅ Redis client ready","timestamp":"2025-06-20 18:36:54:3654"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-20 18:36:54:3654"}
{"level":"info","message":"✅ Redis connection completed","timestamp":"2025-06-20 18:36:54:3654"}
{"level":"info","message":"⚙️ Initializing services...","timestamp":"2025-06-20 18:36:54:3654"}
{"level":"info","message":"🔄 Connecting cache service...","timestamp":"2025-06-20 18:36:54:3654"}
{"level":"info","message":"Redis cache client connecting...","timestamp":"2025-06-20 18:36:54:3654"}
{"level":"info","message":"Redis cache client connected and ready","timestamp":"2025-06-20 18:36:54:3654"}
{"level":"info","message":"✅ Cache service connected","timestamp":"2025-06-20 18:36:54:3654"}
{"level":"info","message":"🔄 Connecting session service...","timestamp":"2025-06-20 18:36:54:3654"}
{"level":"info","message":"Redis session client connecting...","timestamp":"2025-06-20 18:36:55:3655"}
{"level":"info","message":"Redis session client connected and ready","timestamp":"2025-06-20 18:36:55:3655"}
{"level":"info","message":"✅ Session service connected","timestamp":"2025-06-20 18:36:55:3655"}
{"level":"info","message":"🔄 Connecting token service...","timestamp":"2025-06-20 18:36:55:3655"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:36:55:3655"}
{"level":"info","message":"Redis token client connected and ready","timestamp":"2025-06-20 18:36:55:3655"}
{"level":"info","message":"✅ Token service connected","timestamp":"2025-06-20 18:36:55:3655"}
{"level":"info","message":"All services initialized successfully","timestamp":"2025-06-20 18:36:55:3655"}
{"level":"info","message":"✅ Services initialization completed","timestamp":"2025-06-20 18:36:55:3655"}
{"level":"info","message":"🔐 Setting up session middleware...","timestamp":"2025-06-20 18:36:55:3655"}
{"level":"info","message":"🛡️ Setting up rate limiting...","timestamp":"2025-06-20 18:36:55:3655"}
{"level":"info","message":"📋 Setting up API routes...","timestamp":"2025-06-20 18:36:55:3655"}
{"level":"info","message":"📧 Email service initialized (verification disabled for testing)","timestamp":"2025-06-20 18:36:55:3655"}
{"level":"info","message":"Email templates initialized successfully","timestamp":"2025-06-20 18:36:56:3656"}
{"level":"info","message":"🔗 Mounting auth routes...","timestamp":"2025-06-20 18:36:56:3656"}
{"level":"info","message":"🔗 Mounting user routes...","timestamp":"2025-06-20 18:36:56:3656"}
{"level":"info","message":"🔗 Mounting profile routes...","timestamp":"2025-06-20 18:36:56:3656"}
{"level":"info","message":"🔗 Mounting photo routes...","timestamp":"2025-06-20 18:36:56:3656"}
{"level":"info","message":"🔗 Mounting upload routes...","timestamp":"2025-06-20 18:36:56:3656"}
{"level":"info","message":"🔗 Mounting search routes...","timestamp":"2025-06-20 18:36:56:3656"}
{"level":"info","message":"🔗 Mounting property routes...","timestamp":"2025-06-20 18:36:56:3656"}
{"level":"info","message":"🔗 All routes mounted successfully","timestamp":"2025-06-20 18:36:56:3656"}
{"level":"info","message":"✅ API routes configured","timestamp":"2025-06-20 18:36:56:3656"}
{"level":"info","message":"💬 Initializing Socket.IO service...","timestamp":"2025-06-20 18:36:56:3656"}
{"level":"info","message":"✅ Socket.IO service initialized","timestamp":"2025-06-20 18:36:56:3656"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis session client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:38:21:3821"}
{"level":"info","message":"Redis session client connecting...","timestamp":"2025-06-20 18:38:21:3821"}
{"level":"info","message":"Redis session client connected and ready","timestamp":"2025-06-20 18:38:24:3824"}
{"level":"info","message":"🚀 Starting server initialization...","timestamp":"2025-06-20 18:39:45:3945"}
{"level":"info","message":"🚀 Starting LajoSpaces Backend Server...","timestamp":"2025-06-20 18:39:45:3945"}
{"level":"info","message":"📊 Connecting to MongoDB...","timestamp":"2025-06-20 18:39:45:3945"}
{"level":"info","message":"📊 Connecting to MongoDB...","timestamp":"2025-06-20 18:39:45:3945"}
{"level":"info","message":"📊 Mongoose connected to MongoDB","timestamp":"2025-06-20 18:39:48:3948"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-20 18:39:48:3948"}
{"level":"info","message":"📊 Connected to database: lajospaces","timestamp":"2025-06-20 18:39:48:3948"}
{"level":"info","message":"✅ MongoDB connection completed","timestamp":"2025-06-20 18:39:48:3948"}
{"level":"info","message":"🔴 Connecting to Redis...","timestamp":"2025-06-20 18:39:48:3948"}
{"level":"info","message":"🔴 Connecting to Redis...","timestamp":"2025-06-20 18:39:48:3948"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:39:48:3948"}
{"level":"info","message":"✅ Redis client ready","timestamp":"2025-06-20 18:39:49:3949"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-20 18:39:49:3949"}
{"level":"info","message":"✅ Redis connection completed","timestamp":"2025-06-20 18:39:49:3949"}
{"level":"info","message":"⚙️ Initializing services...","timestamp":"2025-06-20 18:39:49:3949"}
{"level":"info","message":"🔄 Connecting cache service...","timestamp":"2025-06-20 18:39:49:3949"}
{"level":"info","message":"Redis cache client connecting...","timestamp":"2025-06-20 18:39:49:3949"}
{"level":"info","message":"Redis cache client connected and ready","timestamp":"2025-06-20 18:39:49:3949"}
{"level":"info","message":"✅ Cache service connected","timestamp":"2025-06-20 18:39:49:3949"}
{"level":"info","message":"🔄 Connecting session service...","timestamp":"2025-06-20 18:39:49:3949"}
{"level":"info","message":"Redis session client connecting...","timestamp":"2025-06-20 18:39:50:3950"}
{"level":"info","message":"Redis session client connected and ready","timestamp":"2025-06-20 18:39:50:3950"}
{"level":"info","message":"✅ Session service connected","timestamp":"2025-06-20 18:39:50:3950"}
{"level":"info","message":"🔄 Connecting token service...","timestamp":"2025-06-20 18:39:50:3950"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:39:50:3950"}
{"level":"info","message":"Redis token client connected and ready","timestamp":"2025-06-20 18:39:50:3950"}
{"level":"info","message":"✅ Token service connected","timestamp":"2025-06-20 18:39:50:3950"}
{"level":"info","message":"All services initialized successfully","timestamp":"2025-06-20 18:39:50:3950"}
{"level":"info","message":"✅ Services initialization completed","timestamp":"2025-06-20 18:39:50:3950"}
{"level":"info","message":"🔐 Setting up session middleware...","timestamp":"2025-06-20 18:39:50:3950"}
{"level":"info","message":"🛡️ Setting up rate limiting...","timestamp":"2025-06-20 18:39:50:3950"}
{"level":"info","message":"📋 Setting up API routes...","timestamp":"2025-06-20 18:39:50:3950"}
{"level":"info","message":"📧 Email service initialized (verification disabled for testing)","timestamp":"2025-06-20 18:39:51:3951"}
{"level":"info","message":"Email templates initialized successfully","timestamp":"2025-06-20 18:39:51:3951"}
{"level":"info","message":"🔗 Mounting auth routes...","timestamp":"2025-06-20 18:39:51:3951"}
{"level":"info","message":"🔗 Mounting user routes...","timestamp":"2025-06-20 18:39:51:3951"}
{"level":"info","message":"🔗 Mounting profile routes...","timestamp":"2025-06-20 18:39:51:3951"}
{"level":"info","message":"🔗 Mounting photo routes...","timestamp":"2025-06-20 18:39:51:3951"}
{"level":"info","message":"🔗 Mounting upload routes...","timestamp":"2025-06-20 18:39:51:3951"}
{"level":"info","message":"🔗 Mounting search routes...","timestamp":"2025-06-20 18:39:51:3951"}
{"level":"info","message":"🔗 Mounting property routes...","timestamp":"2025-06-20 18:39:51:3951"}
{"level":"info","message":"🔗 All routes mounted successfully","timestamp":"2025-06-20 18:39:51:3951"}
{"level":"info","message":"✅ API routes configured","timestamp":"2025-06-20 18:39:51:3951"}
{"level":"info","message":"💬 Initializing Socket.IO service...","timestamp":"2025-06-20 18:39:51:3951"}
{"level":"info","message":"✅ Socket.IO service initialized","timestamp":"2025-06-20 18:39:52:3952"}
{"level":"info","message":"🚀 Starting server initialization...","timestamp":"2025-06-20 18:41:38:4138"}
{"level":"info","message":"🚀 Starting LajoSpaces Backend Server...","timestamp":"2025-06-20 18:41:38:4138"}
{"level":"info","message":"📊 Connecting to MongoDB...","timestamp":"2025-06-20 18:41:38:4138"}
{"level":"info","message":"📊 Connecting to MongoDB...","timestamp":"2025-06-20 18:41:38:4138"}
{"level":"info","message":"📊 Mongoose connected to MongoDB","timestamp":"2025-06-20 18:41:42:4142"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-20 18:41:42:4142"}
{"level":"info","message":"📊 Connected to database: lajospaces","timestamp":"2025-06-20 18:41:42:4142"}
{"level":"info","message":"✅ MongoDB connection completed","timestamp":"2025-06-20 18:41:42:4142"}
{"level":"info","message":"🔴 Connecting to Redis...","timestamp":"2025-06-20 18:41:42:4142"}
{"level":"info","message":"🔴 Connecting to Redis...","timestamp":"2025-06-20 18:41:42:4142"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:41:42:4142"}
{"level":"info","message":"✅ Redis client ready","timestamp":"2025-06-20 18:41:42:4142"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-20 18:41:42:4142"}
{"level":"info","message":"✅ Redis connection completed","timestamp":"2025-06-20 18:41:42:4142"}
{"level":"info","message":"⚙️ Initializing services...","timestamp":"2025-06-20 18:41:42:4142"}
{"level":"info","message":"🔄 Connecting cache service...","timestamp":"2025-06-20 18:41:42:4142"}
{"level":"info","message":"Redis cache client connecting...","timestamp":"2025-06-20 18:41:43:4143"}
{"level":"info","message":"Redis cache client connected and ready","timestamp":"2025-06-20 18:41:43:4143"}
{"level":"info","message":"✅ Cache service connected","timestamp":"2025-06-20 18:41:43:4143"}
{"level":"info","message":"🔄 Connecting session service...","timestamp":"2025-06-20 18:41:43:4143"}
{"level":"info","message":"Redis session client connecting...","timestamp":"2025-06-20 18:41:43:4143"}
{"level":"info","message":"Redis session client connected and ready","timestamp":"2025-06-20 18:41:43:4143"}
{"level":"info","message":"✅ Session service connected","timestamp":"2025-06-20 18:41:43:4143"}
{"level":"info","message":"🔄 Connecting token service...","timestamp":"2025-06-20 18:41:43:4143"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:41:43:4143"}
{"level":"info","message":"Redis token client connected and ready","timestamp":"2025-06-20 18:41:44:4144"}
{"level":"info","message":"✅ Token service connected","timestamp":"2025-06-20 18:41:44:4144"}
{"level":"info","message":"All services initialized successfully","timestamp":"2025-06-20 18:41:44:4144"}
{"level":"info","message":"✅ Services initialization completed","timestamp":"2025-06-20 18:41:44:4144"}
{"level":"info","message":"🔐 Setting up session middleware...","timestamp":"2025-06-20 18:41:44:4144"}
{"level":"info","message":"🛡️ Setting up rate limiting...","timestamp":"2025-06-20 18:41:44:4144"}
{"level":"info","message":"📋 Setting up API routes...","timestamp":"2025-06-20 18:41:44:4144"}
{"level":"info","message":"📧 Email service initialized (verification disabled for testing)","timestamp":"2025-06-20 18:41:44:4144"}
{"level":"info","message":"Email templates initialized successfully","timestamp":"2025-06-20 18:41:45:4145"}
{"level":"info","message":"🔗 Mounting auth routes...","timestamp":"2025-06-20 18:41:45:4145"}
{"level":"info","message":"🔗 Mounting user routes...","timestamp":"2025-06-20 18:41:45:4145"}
{"level":"info","message":"🔗 Mounting profile routes...","timestamp":"2025-06-20 18:41:45:4145"}
{"level":"info","message":"🔗 Mounting photo routes...","timestamp":"2025-06-20 18:41:45:4145"}
{"level":"info","message":"🔗 Mounting upload routes...","timestamp":"2025-06-20 18:41:45:4145"}
{"level":"info","message":"🔗 Mounting search routes...","timestamp":"2025-06-20 18:41:45:4145"}
{"level":"info","message":"🔗 Mounting property routes...","timestamp":"2025-06-20 18:41:45:4145"}
{"level":"info","message":"🔗 All routes mounted successfully","timestamp":"2025-06-20 18:41:45:4145"}
{"level":"info","message":"✅ API routes configured","timestamp":"2025-06-20 18:41:45:4145"}
{"level":"info","message":"💬 Initializing Socket.IO service...","timestamp":"2025-06-20 18:41:45:4145"}
{"level":"info","message":"✅ Socket.IO service initialized","timestamp":"2025-06-20 18:41:45:4145"}
{"level":"info","message":"🚀 Starting server initialization...","timestamp":"2025-06-20 18:43:16:4316"}
{"level":"info","message":"🚀 Starting LajoSpaces Backend Server...","timestamp":"2025-06-20 18:43:16:4316"}
{"level":"info","message":"📊 Connecting to MongoDB...","timestamp":"2025-06-20 18:43:16:4316"}
{"level":"info","message":"📊 Connecting to MongoDB...","timestamp":"2025-06-20 18:43:16:4316"}
{"level":"info","message":"📊 Mongoose connected to MongoDB","timestamp":"2025-06-20 18:43:19:4319"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-20 18:43:19:4319"}
{"level":"info","message":"📊 Connected to database: lajospaces","timestamp":"2025-06-20 18:43:19:4319"}
{"level":"info","message":"✅ MongoDB connection completed","timestamp":"2025-06-20 18:43:19:4319"}
{"level":"info","message":"🔴 Connecting to Redis...","timestamp":"2025-06-20 18:43:19:4319"}
{"level":"info","message":"🔴 Connecting to Redis...","timestamp":"2025-06-20 18:43:19:4319"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:43:20:4320"}
{"level":"info","message":"✅ Redis client ready","timestamp":"2025-06-20 18:43:20:4320"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-20 18:43:20:4320"}
{"level":"info","message":"✅ Redis connection completed","timestamp":"2025-06-20 18:43:20:4320"}
{"level":"info","message":"⚙️ Initializing services...","timestamp":"2025-06-20 18:43:20:4320"}
{"level":"info","message":"🔄 Connecting cache service...","timestamp":"2025-06-20 18:43:20:4320"}
{"level":"info","message":"Redis cache client connecting...","timestamp":"2025-06-20 18:43:20:4320"}
{"level":"info","message":"Redis cache client connected and ready","timestamp":"2025-06-20 18:43:20:4320"}
{"level":"info","message":"✅ Cache service connected","timestamp":"2025-06-20 18:43:20:4320"}
{"level":"info","message":"🔄 Connecting session service...","timestamp":"2025-06-20 18:43:20:4320"}
{"level":"info","message":"Redis session client connecting...","timestamp":"2025-06-20 18:43:21:4321"}
{"level":"info","message":"Redis session client connected and ready","timestamp":"2025-06-20 18:43:21:4321"}
{"level":"info","message":"✅ Session service connected","timestamp":"2025-06-20 18:43:21:4321"}
{"level":"info","message":"🔄 Connecting token service...","timestamp":"2025-06-20 18:43:21:4321"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:43:21:4321"}
{"level":"info","message":"Redis token client connected and ready","timestamp":"2025-06-20 18:43:21:4321"}
{"level":"info","message":"✅ Token service connected","timestamp":"2025-06-20 18:43:21:4321"}
{"level":"info","message":"All services initialized successfully","timestamp":"2025-06-20 18:43:21:4321"}
{"level":"info","message":"✅ Services initialization completed","timestamp":"2025-06-20 18:43:21:4321"}
{"level":"info","message":"🔐 Setting up session middleware...","timestamp":"2025-06-20 18:43:21:4321"}
{"level":"info","message":"🛡️ Setting up rate limiting...","timestamp":"2025-06-20 18:43:21:4321"}
{"level":"info","message":"📋 Setting up API routes...","timestamp":"2025-06-20 18:43:21:4321"}
{"level":"info","message":"📧 Email service initialized (verification disabled for testing)","timestamp":"2025-06-20 18:43:22:4322"}
{"level":"info","message":"🔗 Mounting auth routes...","timestamp":"2025-06-20 18:43:22:4322"}
{"level":"info","message":"🔗 Mounting user routes...","timestamp":"2025-06-20 18:43:22:4322"}
{"level":"info","message":"🔗 Mounting profile routes...","timestamp":"2025-06-20 18:43:22:4322"}
{"level":"info","message":"🔗 Mounting photo routes...","timestamp":"2025-06-20 18:43:22:4322"}
{"level":"info","message":"🔗 Mounting upload routes...","timestamp":"2025-06-20 18:43:22:4322"}
{"level":"info","message":"🔗 Mounting search routes...","timestamp":"2025-06-20 18:43:22:4322"}
{"level":"info","message":"🔗 Mounting property routes...","timestamp":"2025-06-20 18:43:22:4322"}
{"level":"info","message":"🔗 All routes mounted successfully","timestamp":"2025-06-20 18:43:22:4322"}
{"level":"info","message":"✅ API routes configured","timestamp":"2025-06-20 18:43:22:4322"}
{"level":"info","message":"💬 Initializing Socket.IO service...","timestamp":"2025-06-20 18:43:22:4322"}
{"level":"info","message":"✅ Socket.IO service initialized","timestamp":"2025-06-20 18:43:22:4322"}
{"level":"info","message":"🚀 Starting server initialization...","timestamp":"2025-06-20 18:44:42:4442"}
{"level":"info","message":"🚀 Starting LajoSpaces Backend Server...","timestamp":"2025-06-20 18:44:42:4442"}
{"level":"info","message":"📊 Connecting to MongoDB...","timestamp":"2025-06-20 18:44:42:4442"}
{"level":"info","message":"📊 Connecting to MongoDB...","timestamp":"2025-06-20 18:44:42:4442"}
{"level":"info","message":"📊 Mongoose connected to MongoDB","timestamp":"2025-06-20 18:44:45:4445"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-20 18:44:45:4445"}
{"level":"info","message":"📊 Connected to database: lajospaces","timestamp":"2025-06-20 18:44:45:4445"}
{"level":"info","message":"✅ MongoDB connection completed","timestamp":"2025-06-20 18:44:45:4445"}
{"level":"info","message":"🔴 Connecting to Redis...","timestamp":"2025-06-20 18:44:45:4445"}
{"level":"info","message":"🔴 Connecting to Redis...","timestamp":"2025-06-20 18:44:45:4445"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:44:45:4445"}
{"level":"info","message":"✅ Redis client ready","timestamp":"2025-06-20 18:44:46:4446"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-20 18:44:46:4446"}
{"level":"info","message":"✅ Redis connection completed","timestamp":"2025-06-20 18:44:46:4446"}
{"level":"info","message":"⚙️ Initializing services...","timestamp":"2025-06-20 18:44:46:4446"}
{"level":"info","message":"🔄 Connecting cache service...","timestamp":"2025-06-20 18:44:46:4446"}
{"level":"info","message":"Redis cache client connecting...","timestamp":"2025-06-20 18:44:46:4446"}
{"level":"info","message":"Redis cache client connected and ready","timestamp":"2025-06-20 18:44:46:4446"}
{"level":"info","message":"✅ Cache service connected","timestamp":"2025-06-20 18:44:46:4446"}
{"level":"info","message":"🔄 Connecting session service...","timestamp":"2025-06-20 18:44:46:4446"}
{"level":"info","message":"Redis session client connecting...","timestamp":"2025-06-20 18:44:46:4446"}
{"level":"info","message":"Redis session client connected and ready","timestamp":"2025-06-20 18:44:47:4447"}
{"level":"info","message":"✅ Session service connected","timestamp":"2025-06-20 18:44:47:4447"}
{"level":"info","message":"🔄 Connecting token service...","timestamp":"2025-06-20 18:44:47:4447"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:44:47:4447"}
{"level":"info","message":"Redis token client connected and ready","timestamp":"2025-06-20 18:44:47:4447"}
{"level":"info","message":"✅ Token service connected","timestamp":"2025-06-20 18:44:47:4447"}
{"level":"info","message":"All services initialized successfully","timestamp":"2025-06-20 18:44:47:4447"}
{"level":"info","message":"✅ Services initialization completed","timestamp":"2025-06-20 18:44:47:4447"}
{"level":"info","message":"🔐 Setting up session middleware...","timestamp":"2025-06-20 18:44:47:4447"}
{"level":"info","message":"🛡️ Setting up rate limiting...","timestamp":"2025-06-20 18:44:47:4447"}
{"level":"info","message":"📋 Setting up API routes...","timestamp":"2025-06-20 18:44:47:4447"}
{"level":"info","message":"📧 Email service initialized (verification disabled for testing)","timestamp":"2025-06-20 18:44:47:4447"}
{"level":"info","message":"🔗 Mounting auth routes...","timestamp":"2025-06-20 18:44:48:4448"}
{"level":"info","message":"🔗 Mounting user routes...","timestamp":"2025-06-20 18:44:48:4448"}
{"level":"info","message":"🔗 Mounting profile routes...","timestamp":"2025-06-20 18:44:48:4448"}
{"level":"info","message":"🔗 Mounting photo routes...","timestamp":"2025-06-20 18:44:48:4448"}
{"level":"info","message":"🔗 Mounting upload routes...","timestamp":"2025-06-20 18:44:48:4448"}
{"level":"info","message":"🔗 Mounting search routes...","timestamp":"2025-06-20 18:44:48:4448"}
{"level":"info","message":"🔗 Mounting property routes...","timestamp":"2025-06-20 18:44:48:4448"}
{"level":"info","message":"🔗 All routes mounted successfully","timestamp":"2025-06-20 18:44:48:4448"}
{"level":"info","message":"✅ API routes configured","timestamp":"2025-06-20 18:44:48:4448"}
{"level":"info","message":"💬 Initializing Socket.IO service...","timestamp":"2025-06-20 18:44:48:4448"}
{"level":"info","message":"✅ Socket.IO service initialized","timestamp":"2025-06-20 18:44:48:4448"}
{"level":"info","message":"🚀 Starting server initialization...","timestamp":"2025-06-20 18:46:16:4616"}
{"level":"info","message":"🚀 Starting LajoSpaces Backend Server...","timestamp":"2025-06-20 18:46:16:4616"}
{"level":"info","message":"📊 Connecting to MongoDB...","timestamp":"2025-06-20 18:46:16:4616"}
{"level":"info","message":"📊 Connecting to MongoDB...","timestamp":"2025-06-20 18:46:16:4616"}
{"level":"info","message":"📊 Mongoose connected to MongoDB","timestamp":"2025-06-20 18:46:18:4618"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-20 18:46:18:4618"}
{"level":"info","message":"📊 Connected to database: lajospaces","timestamp":"2025-06-20 18:46:18:4618"}
{"level":"info","message":"✅ MongoDB connection completed","timestamp":"2025-06-20 18:46:18:4618"}
{"level":"info","message":"🔴 Connecting to Redis...","timestamp":"2025-06-20 18:46:18:4618"}
{"level":"info","message":"🔴 Connecting to Redis...","timestamp":"2025-06-20 18:46:18:4618"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:46:19:4619"}
{"level":"info","message":"✅ Redis client ready","timestamp":"2025-06-20 18:46:19:4619"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-20 18:46:19:4619"}
{"level":"info","message":"✅ Redis connection completed","timestamp":"2025-06-20 18:46:19:4619"}
{"level":"info","message":"⚙️ Initializing services...","timestamp":"2025-06-20 18:46:19:4619"}
{"level":"info","message":"🔄 Connecting cache service...","timestamp":"2025-06-20 18:46:19:4619"}
{"level":"info","message":"Redis cache client connecting...","timestamp":"2025-06-20 18:46:19:4619"}
{"level":"info","message":"Redis cache client connected and ready","timestamp":"2025-06-20 18:46:20:4620"}
{"level":"info","message":"✅ Cache service connected","timestamp":"2025-06-20 18:46:20:4620"}
{"level":"info","message":"🔄 Connecting session service...","timestamp":"2025-06-20 18:46:20:4620"}
{"level":"info","message":"Redis session client connecting...","timestamp":"2025-06-20 18:46:20:4620"}
{"level":"info","message":"Redis session client connected and ready","timestamp":"2025-06-20 18:46:20:4620"}
{"level":"info","message":"✅ Session service connected","timestamp":"2025-06-20 18:46:20:4620"}
{"level":"info","message":"🔄 Connecting token service...","timestamp":"2025-06-20 18:46:20:4620"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:46:20:4620"}
{"level":"info","message":"Redis token client connected and ready","timestamp":"2025-06-20 18:46:20:4620"}
{"level":"info","message":"✅ Token service connected","timestamp":"2025-06-20 18:46:20:4620"}
{"level":"info","message":"All services initialized successfully","timestamp":"2025-06-20 18:46:20:4620"}
{"level":"info","message":"✅ Services initialization completed","timestamp":"2025-06-20 18:46:20:4620"}
{"level":"info","message":"🔐 Setting up session middleware...","timestamp":"2025-06-20 18:46:20:4620"}
{"level":"info","message":"🛡️ Setting up rate limiting...","timestamp":"2025-06-20 18:46:20:4620"}
{"level":"info","message":"📋 Setting up API routes...","timestamp":"2025-06-20 18:46:20:4620"}
{"level":"info","message":"📧 Email service initialized (verification disabled for testing)","timestamp":"2025-06-20 18:46:21:4621"}
{"level":"info","message":"🔗 Mounting auth routes...","timestamp":"2025-06-20 18:46:21:4621"}
{"level":"info","message":"🔗 Mounting property routes...","timestamp":"2025-06-20 18:46:21:4621"}
{"level":"info","message":"🔗 All routes mounted successfully","timestamp":"2025-06-20 18:46:21:4621"}
{"level":"info","message":"✅ API routes configured","timestamp":"2025-06-20 18:46:21:4621"}
{"level":"info","message":"💬 Initializing Socket.IO service...","timestamp":"2025-06-20 18:46:21:4621"}
{"level":"info","message":"✅ Socket.IO service initialized","timestamp":"2025-06-20 18:46:21:4621"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis cache client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:53:12:5312"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis session client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:53:12:5312"}
{"level":"info","message":"Redis cache client reconnecting... (attempt 1)","timestamp":"2025-06-20 18:53:12:5312"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis token client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:53:12:5312"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis session client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:53:12:5312"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis token client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:53:13:5313"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis token client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:53:14:5314"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:53:14:5314"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:53:15:5315"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:53:16:5316"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis session client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:53:16:5316"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis cache client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:53:17:5317"}
{"level":"info","message":"Redis cache client reconnecting... (attempt 1)","timestamp":"2025-06-20 18:53:17:5317"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis cache client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:53:17:5317"}
{"level":"info","message":"Redis cache client reconnecting... (attempt 1)","timestamp":"2025-06-20 18:53:17:5317"}
{"level":"error","message":"Redis cache client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:53:18:5318"}
{"level":"error","message":"Redis session client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:53:18:5318"}
{"level":"error","message":"Redis token client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:53:18:5318"}
{"level":"error","message":"Redis session client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:53:18:5318"}
{"level":"info","message":"Redis cache client reconnecting... (attempt 2)","timestamp":"2025-06-20 18:53:18:5318"}
{"level":"error","message":"Redis token client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:53:18:5318"}
{"level":"error","message":"Redis token client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:53:19:5319"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:53:19:5319"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:53:20:5320"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:53:21:5321"}
{"level":"error","message":"Redis session client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:53:21:5321"}
{"level":"error","message":"Redis cache client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:53:22:5322"}
{"level":"info","message":"Redis cache client reconnecting... (attempt 2)","timestamp":"2025-06-20 18:53:22:5322"}
{"level":"error","message":"Redis cache client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:53:22:5322"}
{"level":"info","message":"Redis cache client reconnecting... (attempt 2)","timestamp":"2025-06-20 18:53:22:5322"}
{"level":"error","message":"Redis cache client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:53:23:5323"}
{"level":"error","message":"Redis session client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:53:23:5323"}
{"level":"error","message":"Redis token client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:53:23:5323"}
{"level":"error","message":"Redis token client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:53:23:5323"}
{"level":"error","message":"Redis session client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:53:23:5323"}
{"level":"info","message":"Redis cache client reconnecting... (attempt 3)","timestamp":"2025-06-20 18:53:23:5323"}
{"level":"error","message":"Redis token client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:53:24:5324"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:53:24:5324"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:53:25:5325"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:53:26:5326"}
{"level":"error","message":"Redis session client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:53:26:5326"}
{"level":"error","message":"Redis cache client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:53:27:5327"}
{"level":"info","message":"Redis cache client reconnecting... (attempt 3)","timestamp":"2025-06-20 18:53:27:5327"}
{"level":"error","message":"Redis cache client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:53:27:5327"}
{"level":"info","message":"Redis cache client reconnecting... (attempt 3)","timestamp":"2025-06-20 18:53:27:5327"}
{"level":"error","message":"Redis token client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:53:28:5328"}
{"level":"error","message":"Redis session client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:53:28:5328"}
{"level":"error","message":"Redis token client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:53:28:5328"}
{"level":"error","message":"Redis session client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:53:28:5328"}
{"level":"error","message":"Redis cache client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:53:28:5328"}
{"level":"info","message":"Redis cache client reconnecting... (attempt 4)","timestamp":"2025-06-20 18:53:28:5328"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:53:29:5329"}
{"level":"error","message":"Redis token client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:53:29:5329"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:53:30:5330"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:53:31:5331"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:53:31:5331"}
{"level":"info","message":"✅ Redis client ready","timestamp":"2025-06-20 18:53:31:5331"}
{"level":"error","message":"Redis session client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:53:31:5331"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:53:31:5331"}
{"level":"info","message":"Redis session client connecting...","timestamp":"2025-06-20 18:53:31:5331"}
{"level":"info","message":"Redis cache client connecting...","timestamp":"2025-06-20 18:53:31:5331"}
{"level":"info","message":"Redis session client connecting...","timestamp":"2025-06-20 18:53:31:5331"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:53:31:5331"}
{"level":"info","message":"Redis session client connecting...","timestamp":"2025-06-20 18:53:32:5332"}
{"level":"info","message":"Redis token client connected and ready","timestamp":"2025-06-20 18:53:32:5332"}
{"level":"info","message":"Redis session client connected and ready","timestamp":"2025-06-20 18:53:32:5332"}
{"level":"info","message":"Redis cache client connected and ready","timestamp":"2025-06-20 18:53:32:5332"}
{"level":"info","message":"Redis token client connected and ready","timestamp":"2025-06-20 18:53:32:5332"}
{"level":"info","message":"Redis session client connected and ready","timestamp":"2025-06-20 18:53:32:5332"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:53:32:5332"}
{"level":"error","message":"Redis cache client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:53:32:5332"}
{"level":"info","message":"Redis session client connected and ready","timestamp":"2025-06-20 18:53:32:5332"}
{"level":"info","message":"Redis cache client reconnecting... (attempt 4)","timestamp":"2025-06-20 18:53:32:5332"}
{"level":"error","message":"Redis cache client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:53:32:5332"}
{"level":"info","message":"Redis cache client connecting...","timestamp":"2025-06-20 18:53:32:5332"}
{"level":"info","message":"Redis cache client reconnecting... (attempt 4)","timestamp":"2025-06-20 18:53:32:5332"}
{"level":"info","message":"Redis cache client connected and ready","timestamp":"2025-06-20 18:53:32:5332"}
{"level":"info","message":"✅ Redis client ready","timestamp":"2025-06-20 18:53:32:5332"}
{"level":"info","message":"Redis cache client connecting...","timestamp":"2025-06-20 18:53:33:5333"}
{"level":"info","message":"Redis cache client connected and ready","timestamp":"2025-06-20 18:53:33:5333"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:53:33:5333"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:53:33:5333"}
{"level":"info","message":"Redis token client connected and ready","timestamp":"2025-06-20 18:53:33:5333"}
{"level":"info","message":"✅ Redis client ready","timestamp":"2025-06-20 18:53:33:5333"}
{"level":"info","message":"📊 Mongoose disconnected from MongoDB","timestamp":"2025-06-20 18:53:55:5355"}
{"level":"info","message":"📊 Mongoose connected to MongoDB","timestamp":"2025-06-20 18:53:56:5356"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:57:35:5735"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis token client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:57:35:5735"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis cache client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:57:35:5735"}
{"level":"info","message":"Redis cache client reconnecting... (attempt 1)","timestamp":"2025-06-20 18:57:36:5736"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis session client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:57:36:5736"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:57:36:5736"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis token client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:57:36:5736"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis session client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:57:36:5736"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis cache client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:57:36:5736"}
{"level":"info","message":"Redis cache client reconnecting... (attempt 1)","timestamp":"2025-06-20 18:57:36:5736"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis token client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:57:36:5736"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis cache client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:57:36:5736"}
{"level":"info","message":"Redis cache client reconnecting... (attempt 1)","timestamp":"2025-06-20 18:57:36:5736"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis session client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:57:36:5736"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:57:36:5736"}
{"level":"error","message":"Redis session client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:57:41:5741"}
{"level":"error","message":"Redis session client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:57:41:5741"}
{"level":"error","message":"Redis cache client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:57:41:5741"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:57:41:5741"}
{"level":"error","message":"Redis token client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:57:41:5741"}
{"level":"error","message":"Redis cache client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:57:41:5741"}
{"level":"error","message":"Redis cache client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:57:41:5741"}
{"level":"error","message":"Redis token client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:57:41:5741"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:57:41:5741"}
{"level":"info","message":"Redis cache client reconnecting... (attempt 2)","timestamp":"2025-06-20 18:57:41:5741"}
{"level":"info","message":"Redis cache client reconnecting... (attempt 2)","timestamp":"2025-06-20 18:57:41:5741"}
{"level":"error","message":"Redis session client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:57:41:5741"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:57:41:5741"}
{"level":"error","message":"Redis token client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:57:41:5741"}
{"level":"info","message":"Redis cache client reconnecting... (attempt 2)","timestamp":"2025-06-20 18:57:41:5741"}
{"level":"error","message":"Redis session client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:57:46:5746"}
{"level":"error","message":"Redis cache client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:57:46:5746"}
{"level":"error","message":"Redis session client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:57:46:5746"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:57:46:5746"}
{"level":"error","message":"Redis token client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:57:46:5746"}
{"level":"error","message":"Redis cache client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:57:46:5746"}
{"level":"error","message":"Redis cache client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:57:46:5746"}
{"level":"error","message":"Redis token client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:57:46:5746"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:57:46:5746"}
{"level":"error","message":"Redis session client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:57:46:5746"}
{"level":"error","message":"Redis token client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:57:46:5746"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:57:46:5746"}
{"level":"info","message":"Redis cache client reconnecting... (attempt 3)","timestamp":"2025-06-20 18:57:46:5746"}
{"level":"info","message":"Redis cache client reconnecting... (attempt 3)","timestamp":"2025-06-20 18:57:46:5746"}
{"level":"info","message":"Redis cache client reconnecting... (attempt 3)","timestamp":"2025-06-20 18:57:46:5746"}
{"level":"error","message":"Redis session client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:57:51:5751"}
{"level":"error","message":"Redis session client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:57:51:5751"}
{"level":"error","message":"Redis session client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:57:51:5751"}
{"level":"error","message":"Redis token client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:57:51:5751"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:57:51:5751"}
{"level":"error","message":"Redis cache client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:57:51:5751"}
{"level":"error","message":"Redis token client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:57:51:5751"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:57:51:5751"}
{"level":"error","message":"Redis cache client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:57:51:5751"}
{"level":"error","message":"Redis token client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:57:51:5751"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:57:51:5751"}
{"level":"error","message":"Redis cache client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:57:51:5751"}
{"level":"info","message":"Redis cache client reconnecting... (attempt 4)","timestamp":"2025-06-20 18:57:51:5751"}
{"level":"info","message":"Redis cache client reconnecting... (attempt 4)","timestamp":"2025-06-20 18:57:51:5751"}
{"level":"info","message":"Redis cache client reconnecting... (attempt 4)","timestamp":"2025-06-20 18:57:51:5751"}
{"level":"error","message":"Redis token client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:57:56:5756"}
{"level":"error","message":"Redis token client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:57:56:5756"}
{"level":"error","message":"Redis session client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:57:56:5756"}
{"level":"error","message":"Redis session client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:57:56:5756"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:57:56:5756"}
{"level":"error","message":"Redis cache client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:57:56:5756"}
{"level":"error","message":"Redis token client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:57:56:5756"}
{"level":"error","message":"Redis session client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:57:56:5756"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:57:56:5756"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:57:56:5756"}
{"level":"error","message":"Redis cache client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:57:56:5756"}
{"level":"error","message":"Redis cache client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:57:56:5756"}
{"level":"info","message":"Redis cache client reconnecting... (attempt 5)","timestamp":"2025-06-20 18:57:57:5757"}
{"level":"info","message":"Redis cache client reconnecting... (attempt 5)","timestamp":"2025-06-20 18:57:57:5757"}
{"level":"info","message":"Redis cache client reconnecting... (attempt 5)","timestamp":"2025-06-20 18:57:57:5757"}
{"level":"error","message":"Redis session client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:58:02:582"}
{"level":"error","message":"Redis session client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:58:02:582"}
{"level":"error","message":"Redis token client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:58:02:582"}
{"level":"error","message":"Redis session client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:58:02:582"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:58:02:582"}
{"level":"error","message":"Redis token client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:58:02:582"}
{"level":"error","message":"Redis token client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:58:02:582"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:58:02:582"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:58:02:582"}
{"level":"error","message":"Redis cache client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:58:02:582"}
{"level":"error","message":"Redis cache client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:58:02:582"}
{"level":"error","message":"Redis cache client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:58:02:582"}
{"level":"info","message":"Redis cache client reconnecting... (attempt 6)","timestamp":"2025-06-20 18:58:02:582"}
{"level":"info","message":"Redis cache client reconnecting... (attempt 6)","timestamp":"2025-06-20 18:58:02:582"}
{"level":"info","message":"Redis cache client reconnecting... (attempt 6)","timestamp":"2025-06-20 18:58:02:582"}
{"level":"error","message":"Redis session client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:58:07:587"}
{"level":"error","message":"Redis session client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:58:07:587"}
{"level":"error","message":"Redis token client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:58:07:587"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:58:07:587"}
{"level":"error","message":"Redis token client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:58:07:587"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:58:07:587"}
{"level":"error","message":"Redis session client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:58:07:587"}
{"level":"error","message":"Redis token client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:58:07:587"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:58:07:587"}
{"level":"error","message":"Redis cache client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:58:07:587"}
{"level":"error","message":"Redis cache client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:58:07:587"}
{"level":"error","message":"Redis cache client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:58:07:587"}
{"level":"info","message":"Redis cache client reconnecting... (attempt 7)","timestamp":"2025-06-20 18:58:08:588"}
{"level":"info","message":"Redis cache client reconnecting... (attempt 7)","timestamp":"2025-06-20 18:58:08:588"}
{"level":"info","message":"Redis cache client reconnecting... (attempt 7)","timestamp":"2025-06-20 18:58:08:588"}
{"level":"info","message":"Redis cache client connecting...","timestamp":"2025-06-20 18:58:11:5811"}
{"level":"info","message":"Redis cache client connecting...","timestamp":"2025-06-20 18:58:11:5811"}
{"level":"info","message":"Redis cache client connecting...","timestamp":"2025-06-20 18:58:11:5811"}
{"level":"info","message":"Redis cache client connected and ready","timestamp":"2025-06-20 18:58:11:5811"}
{"level":"info","message":"Redis cache client connected and ready","timestamp":"2025-06-20 18:58:11:5811"}
{"level":"info","message":"Redis cache client connected and ready","timestamp":"2025-06-20 18:58:12:5812"}
{"level":"error","message":"Redis token client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:58:12:5812"}
{"level":"error","message":"Redis session client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:58:12:5812"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:58:12:5812"}
{"level":"error","message":"Redis session client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:58:12:5812"}
{"level":"error","message":"Redis token client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:58:12:5812"}
{"level":"error","message":"Redis token client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:58:12:5812"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:58:12:5812"}
{"level":"error","message":"❌ Redis connection error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:58:12:5812"}
{"level":"error","message":"Redis session client error: Connection timeout","stack":"Error: Connection timeout\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\@redis\\client\\dist\\lib\\client\\socket.js:177:124)\n    at Object.onceWrapper (node:events:632:28)\n    at Socket.emit (node:events:518:28)\n    at Socket.emit (node:domain:488:12)\n    at Socket._onTimeout (node:net:589:8)\n    at listOnTimeout (node:internal/timers:573:17)\n    at processTimers (node:internal/timers:514:7)","timestamp":"2025-06-20 18:58:12:5812"}
{"level":"info","message":"Redis session client connecting...","timestamp":"2025-06-20 18:58:13:5813"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:58:13:5813"}
{"level":"info","message":"Redis session client connecting...","timestamp":"2025-06-20 18:58:13:5813"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:13:5813"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:13:5813"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:58:13:5813"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:58:13:5813"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:13:5813"}
{"level":"info","message":"Redis session client connecting...","timestamp":"2025-06-20 18:58:13:5813"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:13:5813"}
{"level":"info","message":"Redis session client connected and ready","timestamp":"2025-06-20 18:58:13:5813"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:13:5813"}
{"level":"info","message":"Redis token client connected and ready","timestamp":"2025-06-20 18:58:13:5813"}
{"level":"info","message":"Redis session client connected and ready","timestamp":"2025-06-20 18:58:13:5813"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:13:5813"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:13:5813"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:13:5813"}
{"level":"error","message":"Redis session client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:13:5813"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:13:5813"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:14:5814"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:14:5814"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:58:14:5814"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:58:14:5814"}
{"level":"info","message":"Redis session client connecting...","timestamp":"2025-06-20 18:58:14:5814"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:14:5814"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:14:5814"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:14:5814"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:14:5814"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:14:5814"}
{"level":"error","message":"Redis session client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:14:5814"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:14:5814"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:15:5815"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:58:15:5815"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:15:5815"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:58:15:5815"}
{"level":"info","message":"Redis session client connecting...","timestamp":"2025-06-20 18:58:15:5815"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:15:5815"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:15:5815"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:15:5815"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:15:5815"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:15:5815"}
{"level":"error","message":"Redis session client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:15:5815"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:15:5815"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:15:5815"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:58:15:5815"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:16:5816"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:16:5816"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:58:16:5816"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:16:5816"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:16:5816"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:58:16:5816"}
{"level":"info","message":"Redis session client connecting...","timestamp":"2025-06-20 18:58:16:5816"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:16:5816"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:17:5817"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:17:5817"}
{"level":"error","message":"Redis session client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:17:5817"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:17:5817"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:17:5817"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:17:5817"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:17:5817"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:58:17:5817"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:17:5817"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:17:5817"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:58:17:5817"}
{"level":"info","message":"Redis session client connecting...","timestamp":"2025-06-20 18:58:17:5817"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:17:5817"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:18:5818"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:18:5818"}
{"level":"error","message":"Redis session client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:18:5818"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:18:5818"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis token client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:18:5818"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis token client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:18:5818"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:18:5818"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:18:5818"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:18:5818"}
{"level":"info","message":"Redis session client connecting...","timestamp":"2025-06-20 18:58:18:5818"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:58:18:5818"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:19:5819"}
{"level":"error","message":"Redis session client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:19:5819"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:19:5819"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:58:19:5819"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:19:5819"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:19:5819"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:19:5819"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:19:5819"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:19:5819"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:19:5819"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:58:19:5819"}
{"level":"info","message":"Redis session client connecting...","timestamp":"2025-06-20 18:58:19:5819"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:19:5819"}
{"level":"error","message":"Redis session client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:19:5819"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:20:5820"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:20:5820"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:20:5820"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:58:20:5820"}
{"level":"info","message":"Redis session client connecting...","timestamp":"2025-06-20 18:58:20:5820"}
{"level":"error","message":"Redis session client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:20:5820"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:20:5820"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:20:5820"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:58:21:5821"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:21:5821"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:21:5821"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis token client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:21:5821"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis token client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:21:5821"}
{"level":"info","message":"Redis session client connecting...","timestamp":"2025-06-20 18:58:21:5821"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:21:5821"}
{"level":"error","message":"Redis session client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:21:5821"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:21:5821"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:21:5821"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:21:5821"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:58:21:5821"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:58:22:5822"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:22:5822"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis token client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:22:5822"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis token client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:22:5822"}
{"level":"info","message":"Redis session client connecting...","timestamp":"2025-06-20 18:58:22:5822"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:22:5822"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:22:5822"}
{"level":"error","message":"Redis session client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:22:5822"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:22:5822"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:22:5822"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:58:22:5822"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:23:5823"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:58:23:5823"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:23:5823"}
{"level":"info","message":"Redis session client connecting...","timestamp":"2025-06-20 18:58:23:5823"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:23:5823"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:23:5823"}
{"level":"error","message":"Redis session client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:23:5823"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:23:5823"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:23:5823"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:23:5823"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:58:23:5823"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:23:5823"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:58:24:5824"}
{"level":"info","message":"Redis session client connecting...","timestamp":"2025-06-20 18:58:24:5824"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:24:5824"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:24:5824"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:24:5824"}
{"level":"error","message":"Redis session client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:24:5824"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:24:5824"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:24:5824"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:58:24:5824"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis token client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:25:5825"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis token client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:25:5825"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:25:5825"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:25:5825"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:58:25:5825"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:25:5825"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:25:5825"}
{"level":"info","message":"Redis session client connecting...","timestamp":"2025-06-20 18:58:25:5825"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:25:5825"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:25:5825"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:25:5825"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:58:25:5825"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:25:5825"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:25:5825"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis session client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:25:5825"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis session client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:25:5825"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:25:5825"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:25:5825"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:26:5826"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:26:5826"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:58:26:5826"}
{"level":"info","message":"📊 Mongoose disconnected from MongoDB","timestamp":"2025-06-20 18:58:26:5826"}
{"level":"info","message":"📊 Mongoose disconnected from MongoDB","timestamp":"2025-06-20 18:58:26:5826"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:26:5826"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:26:5826"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:26:5826"}
{"level":"info","message":"📊 Mongoose connected to MongoDB","timestamp":"2025-06-20 18:58:26:5826"}
{"level":"info","message":"📊 Mongoose connected to MongoDB","timestamp":"2025-06-20 18:58:26:5826"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:58:27:5827"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:27:5827"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:27:5827"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:27:5827"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:27:5827"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:27:5827"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:58:27:5827"}
{"level":"info","message":"Redis session client connecting...","timestamp":"2025-06-20 18:58:27:5827"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:27:5827"}
{"level":"error","message":"Redis session client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:27:5827"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:27:5827"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:58:27:5827"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:28:5828"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:28:5828"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:28:5828"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis token client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:28:5828"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis token client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:28:5828"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:28:5828"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:28:5828"}
{"level":"info","message":"Redis session client connecting...","timestamp":"2025-06-20 18:58:28:5828"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:28:5828"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:28:5828"}
{"level":"error","message":"Redis session client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:28:5828"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:58:28:5828"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:28:5828"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:58:28:5828"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:29:5829"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:29:5829"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:29:5829"}
{"level":"info","message":"Redis session client connecting...","timestamp":"2025-06-20 18:58:29:5829"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:29:5829"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis token client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:29:5829"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis token client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:29:5829"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:29:5829"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:29:5829"}
{"level":"error","message":"Redis session client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:29:5829"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis token client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:29:5829"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis token client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:29:5829"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:29:5829"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:30:5830"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:30:5830"}
{"level":"info","message":"Redis session client connecting...","timestamp":"2025-06-20 18:58:30:5830"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:30:5830"}
{"level":"error","message":"Redis session client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:30:5830"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:30:5830"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:31:5831"}
{"level":"info","message":"Redis session client connecting...","timestamp":"2025-06-20 18:58:31:5831"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:58:31:5831"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:31:5831"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:58:31:5831"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:31:5831"}
{"level":"error","message":"Redis session client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:31:5831"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:31:5831"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:31:5831"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:31:5831"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:31:5831"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:31:5831"}
{"level":"info","message":"Redis session client connecting...","timestamp":"2025-06-20 18:58:32:5832"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:32:5832"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:58:32:5832"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:32:5832"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:58:32:5832"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:32:5832"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis session client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:32:5832"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:32:5832"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis session client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:32:5832"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:32:5832"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:32:5832"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:32:5832"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:33:5833"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:33:5833"}
{"level":"info","message":"Redis session client connecting...","timestamp":"2025-06-20 18:58:33:5833"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:33:5833"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:58:33:5833"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:33:5833"}
{"level":"error","message":"Redis session client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:33:5833"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:33:5833"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:33:5833"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:33:5833"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:33:5833"}
{"level":"info","message":"Redis session client connecting...","timestamp":"2025-06-20 18:58:34:5834"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:34:5834"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:34:5834"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:58:34:5834"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:34:5834"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:34:5834"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis token client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:34:5834"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis token client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:34:5834"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:34:5834"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:34:5834"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis session client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:34:5834"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis session client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:34:5834"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:34:5834"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:34:5834"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:58:35:5835"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:35:5835"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:35:5835"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:35:5835"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:35:5835"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:35:5835"}
{"level":"info","message":"Redis session client connecting...","timestamp":"2025-06-20 18:58:35:5835"}
{"level":"error","message":"Redis session client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:35:5835"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:35:5835"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:35:5835"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:58:35:5835"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:36:5836"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:36:5836"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:36:5836"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:36:5836"}
{"level":"info","message":"Redis session client connecting...","timestamp":"2025-06-20 18:58:36:5836"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:36:5836"}
{"level":"error","message":"Redis session client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:36:5836"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:36:5836"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:58:36:5836"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:36:5836"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:36:5836"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:37:5837"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:37:5837"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:37:5837"}
{"level":"info","message":"Redis session client connecting...","timestamp":"2025-06-20 18:58:37:5837"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:37:5837"}
{"level":"error","message":"Redis session client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:37:5837"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:37:5837"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis token client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:37:5837"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis token client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:37:5837"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:58:37:5837"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:37:5837"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:37:5837"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:37:5837"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:37:5837"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:37:5837"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:38:5838"}
{"level":"info","message":"Redis session client connecting...","timestamp":"2025-06-20 18:58:38:5838"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:58:38:5838"}
{"level":"error","message":"Redis session client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:38:5838"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:38:5838"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:38:5838"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:38:5838"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:58:38:5838"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:38:5838"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:38:5838"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:38:5838"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:38:5838"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:39:5839"}
{"level":"info","message":"Redis session client connecting...","timestamp":"2025-06-20 18:58:39:5839"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:58:39:5839"}
{"level":"error","message":"Redis session client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:39:5839"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:39:5839"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:39:5839"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:58:39:5839"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:39:5839"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:39:5839"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:39:5839"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:39:5839"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:39:5839"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:39:5839"}
{"level":"info","message":"Redis session client connecting...","timestamp":"2025-06-20 18:58:40:5840"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:58:40:5840"}
{"level":"error","message":"Redis session client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:40:5840"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:40:5840"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:40:5840"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:40:5840"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:58:40:5840"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:40:5840"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:40:5840"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:40:5840"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:40:5840"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:40:5840"}
{"level":"info","message":"Redis session client connecting...","timestamp":"2025-06-20 18:58:41:5841"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:58:41:5841"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:58:41:5841"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:41:5841"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis session client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:41:5841"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis session client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:41:5841"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:41:5841"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:41:5841"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis token client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:41:5841"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis token client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:41:5841"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:58:42:5842"}
{"level":"info","message":"Redis session client connecting...","timestamp":"2025-06-20 18:58:42:5842"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:42:5842"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:42:5842"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:42:5842"}
{"level":"error","message":"Redis session client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:42:5842"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:42:5842"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:42:5842"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:42:5842"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:58:42:5842"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:42:5842"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:42:5842"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:58:43:5843"}
{"level":"info","message":"Redis session client connecting...","timestamp":"2025-06-20 18:58:43:5843"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:43:5843"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:43:5843"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:43:5843"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:43:5843"}
{"level":"error","message":"Redis session client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:43:5843"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:43:5843"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:43:5843"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:58:43:5843"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:43:5843"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:43:5843"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:58:44:5844"}
{"level":"info","message":"Redis session client connecting...","timestamp":"2025-06-20 18:58:44:5844"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:44:5844"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:44:5844"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:44:5844"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:44:5844"}
{"level":"error","message":"Redis session client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:44:5844"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:44:5844"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:44:5844"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:58:44:5844"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:44:5844"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:44:5844"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:58:44:5844"}
{"level":"info","message":"Redis session client connecting...","timestamp":"2025-06-20 18:58:44:5844"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:45:5845"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:45:5845"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:45:5845"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:45:5845"}
{"level":"error","message":"Redis session client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:45:5845"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:58:45:5845"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:45:5845"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:45:5845"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:45:5845"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:45:5845"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:45:5845"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:45:5845"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:58:45:5845"}
{"level":"info","message":"Redis session client connecting...","timestamp":"2025-06-20 18:58:45:5845"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:45:5845"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:45:5845"}
{"level":"error","message":"Redis session client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:46:5846"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:46:5846"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:58:46:5846"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:46:5846"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:46:5846"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:46:5846"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:46:5846"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:46:5846"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:58:46:5846"}
{"level":"info","message":"Redis session client connecting...","timestamp":"2025-06-20 18:58:46:5846"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:46:5846"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:46:5846"}
{"level":"error","message":"Redis session client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:46:5846"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:47:5847"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:58:47:5847"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:47:5847"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:47:5847"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:47:5847"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:47:5847"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:47:5847"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:58:47:5847"}
{"level":"info","message":"Redis session client connecting...","timestamp":"2025-06-20 18:58:47:5847"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:47:5847"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:47:5847"}
{"level":"error","message":"Redis session client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:47:5847"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:47:5847"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:58:48:5848"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:48:5848"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:48:5848"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:48:5848"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:48:5848"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:48:5848"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:58:48:5848"}
{"level":"info","message":"Redis session client connecting...","timestamp":"2025-06-20 18:58:48:5848"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:48:5848"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:48:5848"}
{"level":"error","message":"Redis session client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:48:5848"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:48:5848"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:58:48:5848"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:49:5849"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:49:5849"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:49:5849"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:49:5849"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:49:5849"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:58:49:5849"}
{"level":"info","message":"Redis session client connecting...","timestamp":"2025-06-20 18:58:49:5849"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:49:5849"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:49:5849"}
{"level":"error","message":"Redis session client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:49:5849"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:49:5849"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:58:49:5849"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:49:5849"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:49:5849"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:50:5850"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:50:5850"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:50:5850"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:58:50:5850"}
{"level":"info","message":"Redis session client connecting...","timestamp":"2025-06-20 18:58:50:5850"}
{"level":"info","message":"📊 Mongoose disconnected from MongoDB","timestamp":"2025-06-20 18:58:50:5850"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:50:5850"}
{"level":"error","message":"Redis session client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:50:5850"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:58:50:5850"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:50:5850"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:50:5850"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:50:5850"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis token client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:50:5850"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis token client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:50:5850"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:51:5851"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:51:5851"}
{"level":"info","message":"Redis session client connecting...","timestamp":"2025-06-20 18:58:51:5851"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:51:5851"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:51:5851"}
{"level":"error","message":"Redis session client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:51:5851"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:58:51:5851"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:58:51:5851"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:51:5851"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:51:5851"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:51:5851"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:51:5851"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:51:5851"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:51:5851"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:51:5851"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:52:5852"}
{"level":"info","message":"Redis session client connecting...","timestamp":"2025-06-20 18:58:52:5852"}
{"level":"error","message":"Redis session client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:52:5852"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:58:52:5852"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:58:52:5852"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:52:5852"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:52:5852"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:52:5852"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:52:5852"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:52:5852"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:52:5852"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:53:5853"}
{"level":"info","message":"Redis session client connecting...","timestamp":"2025-06-20 18:58:53:5853"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis token client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:53:5853"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis token client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:53:5853"}
{"level":"error","message":"Redis session client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:53:5853"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:58:53:5853"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:53:5853"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:53:5853"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:53:5853"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:53:5853"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:53:5853"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:53:5853"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:53:5853"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:58:53:5853"}
{"level":"info","message":"Redis session client connecting...","timestamp":"2025-06-20 18:58:54:5854"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:54:5854"}
{"level":"error","message":"Redis session client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:54:5854"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:58:54:5854"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:54:5854"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:54:5854"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:54:5854"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:54:5854"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:54:5854"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:54:5854"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:54:5854"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:58:54:5854"}
{"level":"info","message":"Redis session client connecting...","timestamp":"2025-06-20 18:58:54:5854"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:55:5855"}
{"level":"error","message":"Redis session client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:55:5855"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:58:55:5855"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:55:5855"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:55:5855"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:55:5855"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:55:5855"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:55:5855"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:55:5855"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:55:5855"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:58:55:5855"}
{"level":"info","message":"Redis session client connecting...","timestamp":"2025-06-20 18:58:55:5855"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:55:5855"}
{"level":"error","message":"Redis session client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:56:5856"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:58:56:5856"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:56:5856"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:56:5856"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:56:5856"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:56:5856"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:56:5856"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:56:5856"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:56:5856"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:58:56:5856"}
{"level":"info","message":"Redis session client connecting...","timestamp":"2025-06-20 18:58:56:5856"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:56:5856"}
{"level":"error","message":"Redis session client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:56:5856"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:58:57:5857"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:57:5857"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:57:5857"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:57:5857"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:57:5857"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:57:5857"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:57:5857"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:57:5857"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:58:57:5857"}
{"level":"info","message":"Redis session client connecting...","timestamp":"2025-06-20 18:58:57:5857"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:57:5857"}
{"level":"error","message":"Redis session client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:57:5857"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:58:57:5857"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:57:5857"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:58:5858"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:58:5858"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:58:5858"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:58:5858"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:58:5858"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:58:5858"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:58:58:5858"}
{"level":"info","message":"Redis session client connecting...","timestamp":"2025-06-20 18:58:58:5858"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:58:5858"}
{"level":"error","message":"Redis session client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:58:5858"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:58:58:5858"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:58:5858"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:59:5859"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:59:5859"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:59:5859"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:59:5859"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:59:5859"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:59:5859"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:58:59:5859"}
{"level":"info","message":"Redis session client connecting...","timestamp":"2025-06-20 18:58:59:5859"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:59:5859"}
{"level":"error","message":"Redis session client error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:59:5859"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:58:59:5859"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:58:59:5859"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:58:59:5859"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis token client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:59:5859"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis token client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:58:59:5859"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:59:00:590"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:59:00:590"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:59:00:590"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:59:00:590"}
{"level":"info","message":"Redis session client connecting...","timestamp":"2025-06-20 18:59:00:590"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:59:00:590"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:59:00:590"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:59:00:590"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:59:00:590"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:59:00:590"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:59:00:590"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:59:00:590"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis session client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:59:01:591"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis session client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:59:01:591"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:59:01:591"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:59:01:591"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis token client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:59:01:591"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis token client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:59:01:591"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:59:01:591"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:59:01:591"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:59:01:591"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:59:01:591"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:59:01:591"}
{"level":"info","message":"Redis session client connecting...","timestamp":"2025-06-20 18:59:01:591"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:59:01:591"}
{"level":"info","message":"📊 Mongoose connected to MongoDB","timestamp":"2025-06-20 18:59:01:591"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis session client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:59:02:592"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis session client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:59:02:592"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:59:02:592"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:59:02:592"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:59:02:592"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:59:02:592"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:59:02:592"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:59:02:592"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:59:02:592"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:59:02:592"}
{"level":"info","message":"Redis session client connecting...","timestamp":"2025-06-20 18:59:02:592"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:59:02:592"}
{"level":"error","message":"Redis session client error: ERR max number of clients reached","timestamp":"2025-06-20 18:59:02:592"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:59:03:593"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:59:03:593"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:59:03:593"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis token client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:59:03:593"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis token client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:59:03:593"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:59:03:593"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:59:03:593"}
{"level":"info","message":"Redis token client connected and ready","timestamp":"2025-06-20 18:59:03:593"}
{"level":"info","message":"✅ Redis client ready","timestamp":"2025-06-20 18:59:03:593"}
{"level":"info","message":"Redis session client connecting...","timestamp":"2025-06-20 18:59:03:593"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis session client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:59:04:594"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"Redis session client error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:59:04:594"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:59:04:594"}
{"code":"ECONNRESET","errno":-4077,"level":"error","message":"❌ Redis connection error: read ECONNRESET","stack":"Error: read ECONNRESET\n    at TCP.onStreamRead (node:internal/stream_base_commons:217:20)","syscall":"read","timestamp":"2025-06-20 18:59:04:594"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:59:04:594"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:59:04:594"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:59:04:594"}
{"level":"error","message":"Redis token client error: ERR max number of clients reached","timestamp":"2025-06-20 18:59:04:594"}
{"level":"info","message":"Redis session client connecting...","timestamp":"2025-06-20 18:59:04:594"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:59:04:594"}
{"level":"error","message":"Redis session client error: ERR max number of clients reached","timestamp":"2025-06-20 18:59:04:594"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:59:05:595"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:59:05:595"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:59:05:595"}
{"level":"info","message":"✅ Redis client ready","timestamp":"2025-06-20 18:59:05:595"}
{"level":"info","message":"Redis token client connected and ready","timestamp":"2025-06-20 18:59:05:595"}
{"level":"info","message":"Redis session client connecting...","timestamp":"2025-06-20 18:59:05:595"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:59:05:595"}
{"level":"error","message":"Redis session client error: ERR max number of clients reached","timestamp":"2025-06-20 18:59:05:595"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:59:05:595"}
{"level":"info","message":"Redis session client connecting...","timestamp":"2025-06-20 18:59:06:596"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:59:06:596"}
{"level":"error","message":"Redis session client error: ERR max number of clients reached","timestamp":"2025-06-20 18:59:06:596"}
{"level":"error","message":"❌ Redis connection error: ERR max number of clients reached","timestamp":"2025-06-20 18:59:06:596"}
{"level":"info","message":"Redis session client connecting...","timestamp":"2025-06-20 18:59:07:597"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:59:07:597"}
{"level":"info","message":"Redis session client connected and ready","timestamp":"2025-06-20 18:59:07:597"}
{"level":"info","message":"✅ Redis client ready","timestamp":"2025-06-20 18:59:07:597"}
{"level":"info","message":"🚀 Starting server initialization...","timestamp":"2025-06-20 18:59:16:5916"}
{"level":"info","message":"🚀 Starting LajoSpaces Backend Server...","timestamp":"2025-06-20 18:59:16:5916"}
{"level":"info","message":"📊 Connecting to MongoDB...","timestamp":"2025-06-20 18:59:16:5916"}
{"level":"info","message":"📊 Connecting to MongoDB...","timestamp":"2025-06-20 18:59:16:5916"}
{"level":"info","message":"📊 Mongoose connected to MongoDB","timestamp":"2025-06-20 18:59:19:5919"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-20 18:59:19:5919"}
{"level":"info","message":"📊 Connected to database: lajospaces","timestamp":"2025-06-20 18:59:19:5919"}
{"level":"info","message":"✅ MongoDB connection completed","timestamp":"2025-06-20 18:59:19:5919"}
{"level":"info","message":"🔴 Connecting to Redis...","timestamp":"2025-06-20 18:59:19:5919"}
{"level":"info","message":"🔴 Connecting to Redis...","timestamp":"2025-06-20 18:59:19:5919"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 18:59:19:5919"}
{"level":"info","message":"✅ Redis client ready","timestamp":"2025-06-20 18:59:20:5920"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-20 18:59:20:5920"}
{"level":"info","message":"✅ Redis connection completed","timestamp":"2025-06-20 18:59:20:5920"}
{"level":"info","message":"⚙️ Initializing services...","timestamp":"2025-06-20 18:59:20:5920"}
{"level":"info","message":"🔄 Connecting cache service...","timestamp":"2025-06-20 18:59:20:5920"}
{"level":"info","message":"Redis cache client connecting...","timestamp":"2025-06-20 18:59:20:5920"}
{"level":"info","message":"Redis cache client connected and ready","timestamp":"2025-06-20 18:59:20:5920"}
{"level":"info","message":"✅ Cache service connected","timestamp":"2025-06-20 18:59:20:5920"}
{"level":"info","message":"🔄 Connecting session service...","timestamp":"2025-06-20 18:59:20:5920"}
{"level":"info","message":"Redis session client connecting...","timestamp":"2025-06-20 18:59:20:5920"}
{"level":"info","message":"Redis session client connected and ready","timestamp":"2025-06-20 18:59:21:5921"}
{"level":"info","message":"✅ Session service connected","timestamp":"2025-06-20 18:59:21:5921"}
{"level":"info","message":"🔄 Connecting token service...","timestamp":"2025-06-20 18:59:21:5921"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 18:59:21:5921"}
{"level":"info","message":"Redis token client connected and ready","timestamp":"2025-06-20 18:59:21:5921"}
{"level":"info","message":"✅ Token service connected","timestamp":"2025-06-20 18:59:21:5921"}
{"level":"info","message":"All services initialized successfully","timestamp":"2025-06-20 18:59:21:5921"}
{"level":"info","message":"✅ Services initialization completed","timestamp":"2025-06-20 18:59:21:5921"}
{"level":"info","message":"🔐 Setting up session middleware...","timestamp":"2025-06-20 18:59:21:5921"}
{"level":"info","message":"🛡️ Setting up rate limiting...","timestamp":"2025-06-20 18:59:21:5921"}
{"level":"info","message":"📋 Setting up API routes...","timestamp":"2025-06-20 18:59:21:5921"}
{"level":"info","message":"📧 Email service initialized (verification disabled for testing)","timestamp":"2025-06-20 18:59:21:5921"}
{"level":"info","message":"🔗 Mounting auth routes...","timestamp":"2025-06-20 18:59:21:5921"}
{"level":"info","message":"🔗 All routes mounted successfully","timestamp":"2025-06-20 18:59:21:5921"}
{"level":"info","message":"✅ API routes configured","timestamp":"2025-06-20 18:59:21:5921"}
{"level":"info","message":"💬 Initializing Socket.IO service...","timestamp":"2025-06-20 18:59:21:5921"}
{"level":"info","message":"✅ Socket.IO service initialized","timestamp":"2025-06-20 18:59:21:5921"}
{"level":"info","message":"🚀 Starting server initialization...","timestamp":"2025-06-20 19:00:43:043"}
{"level":"info","message":"🚀 Starting LajoSpaces Backend Server...","timestamp":"2025-06-20 19:00:43:043"}
{"level":"info","message":"📊 Connecting to MongoDB...","timestamp":"2025-06-20 19:00:43:043"}
{"level":"info","message":"📊 Connecting to MongoDB...","timestamp":"2025-06-20 19:00:43:043"}
{"level":"info","message":"📊 Mongoose connected to MongoDB","timestamp":"2025-06-20 19:00:47:047"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-20 19:00:47:047"}
{"level":"info","message":"📊 Connected to database: lajospaces","timestamp":"2025-06-20 19:00:47:047"}
{"level":"info","message":"✅ MongoDB connection completed","timestamp":"2025-06-20 19:00:47:047"}
{"level":"info","message":"🔴 Connecting to Redis...","timestamp":"2025-06-20 19:00:47:047"}
{"level":"info","message":"🔴 Connecting to Redis...","timestamp":"2025-06-20 19:00:47:047"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 19:00:48:048"}
{"level":"info","message":"✅ Redis client ready","timestamp":"2025-06-20 19:00:48:048"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-20 19:00:48:048"}
{"level":"info","message":"✅ Redis connection completed","timestamp":"2025-06-20 19:00:48:048"}
{"level":"info","message":"⚙️ Initializing services...","timestamp":"2025-06-20 19:00:48:048"}
{"level":"info","message":"🔄 Connecting cache service...","timestamp":"2025-06-20 19:00:48:048"}
{"level":"info","message":"Redis cache client connecting...","timestamp":"2025-06-20 19:00:48:048"}
{"level":"info","message":"Redis cache client connected and ready","timestamp":"2025-06-20 19:00:49:049"}
{"level":"info","message":"✅ Cache service connected","timestamp":"2025-06-20 19:00:49:049"}
{"level":"info","message":"🔄 Connecting session service...","timestamp":"2025-06-20 19:00:49:049"}
{"level":"info","message":"Redis session client connecting...","timestamp":"2025-06-20 19:00:49:049"}
{"level":"info","message":"Redis session client connected and ready","timestamp":"2025-06-20 19:00:49:049"}
{"level":"info","message":"✅ Session service connected","timestamp":"2025-06-20 19:00:49:049"}
{"level":"info","message":"🔄 Connecting token service...","timestamp":"2025-06-20 19:00:49:049"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 19:00:49:049"}
{"level":"info","message":"Redis token client connected and ready","timestamp":"2025-06-20 19:00:49:049"}
{"level":"info","message":"✅ Token service connected","timestamp":"2025-06-20 19:00:49:049"}
{"level":"info","message":"All services initialized successfully","timestamp":"2025-06-20 19:00:49:049"}
{"level":"info","message":"✅ Services initialization completed","timestamp":"2025-06-20 19:00:49:049"}
{"level":"info","message":"🔐 Setting up session middleware...","timestamp":"2025-06-20 19:00:49:049"}
{"level":"info","message":"🛡️ Setting up rate limiting...","timestamp":"2025-06-20 19:00:49:049"}
{"level":"info","message":"📋 Setting up API routes...","timestamp":"2025-06-20 19:00:49:049"}
{"level":"info","message":"🔗 All routes mounted successfully","timestamp":"2025-06-20 19:00:49:049"}
{"level":"info","message":"✅ API routes configured","timestamp":"2025-06-20 19:00:49:049"}
{"level":"info","message":"💬 Initializing Socket.IO service...","timestamp":"2025-06-20 19:00:49:049"}
{"level":"info","message":"✅ Socket.IO service initialized","timestamp":"2025-06-20 19:00:50:050"}
{"level":"info","message":"🚀 Starting server initialization...","timestamp":"2025-06-20 19:02:14:214"}
{"level":"info","message":"🚀 Starting LajoSpaces Backend Server...","timestamp":"2025-06-20 19:02:14:214"}
{"level":"info","message":"📊 Connecting to MongoDB...","timestamp":"2025-06-20 19:02:14:214"}
{"level":"info","message":"📊 Connecting to MongoDB...","timestamp":"2025-06-20 19:02:14:214"}
{"level":"info","message":"📊 Mongoose connected to MongoDB","timestamp":"2025-06-20 19:02:18:218"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-20 19:02:18:218"}
{"level":"info","message":"📊 Connected to database: lajospaces","timestamp":"2025-06-20 19:02:18:218"}
{"level":"info","message":"✅ MongoDB connection completed","timestamp":"2025-06-20 19:02:18:218"}
{"level":"info","message":"🔴 Connecting to Redis...","timestamp":"2025-06-20 19:02:18:218"}
{"level":"info","message":"🔴 Connecting to Redis...","timestamp":"2025-06-20 19:02:18:218"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 19:02:19:219"}
{"level":"info","message":"✅ Redis client ready","timestamp":"2025-06-20 19:02:19:219"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-20 19:02:19:219"}
{"level":"info","message":"✅ Redis connection completed","timestamp":"2025-06-20 19:02:19:219"}
{"level":"info","message":"⚙️ Initializing services...","timestamp":"2025-06-20 19:02:19:219"}
{"level":"info","message":"🔄 Connecting cache service...","timestamp":"2025-06-20 19:02:19:219"}
{"level":"info","message":"Redis cache client connecting...","timestamp":"2025-06-20 19:02:20:220"}
{"level":"info","message":"Redis cache client connected and ready","timestamp":"2025-06-20 19:02:20:220"}
{"level":"info","message":"✅ Cache service connected","timestamp":"2025-06-20 19:02:20:220"}
{"level":"info","message":"🔄 Connecting session service...","timestamp":"2025-06-20 19:02:20:220"}
{"level":"info","message":"Redis session client connecting...","timestamp":"2025-06-20 19:02:20:220"}
{"level":"info","message":"Redis session client connected and ready","timestamp":"2025-06-20 19:02:20:220"}
{"level":"info","message":"✅ Session service connected","timestamp":"2025-06-20 19:02:20:220"}
{"level":"info","message":"🔄 Connecting token service...","timestamp":"2025-06-20 19:02:20:220"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 19:02:20:220"}
{"level":"info","message":"Redis token client connected and ready","timestamp":"2025-06-20 19:02:21:221"}
{"level":"info","message":"✅ Token service connected","timestamp":"2025-06-20 19:02:21:221"}
{"level":"info","message":"All services initialized successfully","timestamp":"2025-06-20 19:02:21:221"}
{"level":"info","message":"✅ Services initialization completed","timestamp":"2025-06-20 19:02:21:221"}
{"level":"info","message":"🔐 Setting up session middleware...","timestamp":"2025-06-20 19:02:21:221"}
{"level":"info","message":"🛡️ Setting up rate limiting...","timestamp":"2025-06-20 19:02:21:221"}
{"level":"info","message":"📋 Setting up API routes...","timestamp":"2025-06-20 19:02:21:221"}
{"level":"info","message":"🔗 All routes mounted successfully","timestamp":"2025-06-20 19:02:21:221"}
{"level":"info","message":"✅ API routes configured","timestamp":"2025-06-20 19:02:21:221"}
{"level":"info","message":"🚀 Starting server initialization...","timestamp":"2025-06-20 19:03:38:338"}
{"level":"info","message":"🚀 Starting LajoSpaces Backend Server...","timestamp":"2025-06-20 19:03:38:338"}
{"level":"info","message":"📊 Connecting to MongoDB...","timestamp":"2025-06-20 19:03:38:338"}
{"level":"info","message":"📊 Connecting to MongoDB...","timestamp":"2025-06-20 19:03:38:338"}
{"level":"info","message":"📊 Mongoose connected to MongoDB","timestamp":"2025-06-20 19:03:41:341"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-20 19:03:41:341"}
{"level":"info","message":"📊 Connected to database: lajospaces","timestamp":"2025-06-20 19:03:41:341"}
{"level":"info","message":"✅ MongoDB connection completed","timestamp":"2025-06-20 19:03:41:341"}
{"level":"info","message":"🔴 Connecting to Redis...","timestamp":"2025-06-20 19:03:41:341"}
{"level":"info","message":"🔴 Connecting to Redis...","timestamp":"2025-06-20 19:03:41:341"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 19:03:42:342"}
{"level":"info","message":"✅ Redis client ready","timestamp":"2025-06-20 19:03:42:342"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-20 19:03:42:342"}
{"level":"info","message":"✅ Redis connection completed","timestamp":"2025-06-20 19:03:42:342"}
{"level":"info","message":"⚙️ Initializing services...","timestamp":"2025-06-20 19:03:42:342"}
{"level":"info","message":"🔄 Connecting cache service...","timestamp":"2025-06-20 19:03:42:342"}
{"level":"info","message":"Redis cache client connecting...","timestamp":"2025-06-20 19:03:42:342"}
{"level":"info","message":"Redis cache client connected and ready","timestamp":"2025-06-20 19:03:42:342"}
{"level":"info","message":"✅ Cache service connected","timestamp":"2025-06-20 19:03:42:342"}
{"level":"info","message":"🔄 Connecting session service...","timestamp":"2025-06-20 19:03:42:342"}
{"level":"info","message":"Redis session client connecting...","timestamp":"2025-06-20 19:03:43:343"}
{"level":"info","message":"Redis session client connected and ready","timestamp":"2025-06-20 19:03:43:343"}
{"level":"info","message":"✅ Session service connected","timestamp":"2025-06-20 19:03:43:343"}
{"level":"info","message":"🔄 Connecting token service...","timestamp":"2025-06-20 19:03:43:343"}
{"level":"info","message":"Redis token client connecting...","timestamp":"2025-06-20 19:03:43:343"}
{"level":"info","message":"Redis token client connected and ready","timestamp":"2025-06-20 19:03:43:343"}
{"level":"info","message":"✅ Token service connected","timestamp":"2025-06-20 19:03:43:343"}
{"level":"info","message":"All services initialized successfully","timestamp":"2025-06-20 19:03:43:343"}
{"level":"info","message":"✅ Services initialization completed","timestamp":"2025-06-20 19:03:43:343"}
{"level":"info","message":"🔐 Setting up session middleware...","timestamp":"2025-06-20 19:03:43:343"}
{"level":"info","message":"🛡️ Setting up rate limiting...","timestamp":"2025-06-20 19:03:43:343"}
{"level":"info","message":"📋 Setting up API routes...","timestamp":"2025-06-20 19:03:43:343"}
{"level":"info","message":"🔗 All routes mounted successfully","timestamp":"2025-06-20 19:03:43:343"}
{"level":"info","message":"✅ API routes configured","timestamp":"2025-06-20 19:03:43:343"}
{"level":"info","message":"🚀 Starting server initialization...","timestamp":"2025-06-20 19:17:30:1730"}
{"level":"info","message":"🚀 Starting LajoSpaces Backend Server...","timestamp":"2025-06-20 19:17:30:1730"}
{"level":"info","message":"📊 Connecting to MongoDB...","timestamp":"2025-06-20 19:17:30:1730"}
{"level":"info","message":"📊 Connecting to MongoDB...","timestamp":"2025-06-20 19:17:30:1730"}
{"level":"info","message":"📊 Mongoose connected to MongoDB","timestamp":"2025-06-20 19:17:34:1734"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-20 19:17:34:1734"}
{"level":"info","message":"📊 Connected to database: lajospaces","timestamp":"2025-06-20 19:17:34:1734"}
{"level":"info","message":"✅ MongoDB connection completed","timestamp":"2025-06-20 19:17:34:1734"}
{"level":"info","message":"🔴 Connecting to Redis...","timestamp":"2025-06-20 19:17:34:1734"}
{"level":"info","message":"🔴 Connecting to Redis...","timestamp":"2025-06-20 19:17:34:1734"}
{"level":"info","message":"🔴 Redis client connected","timestamp":"2025-06-20 19:17:34:1734"}
{"level":"info","message":"✅ Redis client ready","timestamp":"2025-06-20 19:17:35:1735"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-20 19:17:35:1735"}
{"level":"info","message":"✅ Redis connection completed","timestamp":"2025-06-20 19:17:35:1735"}
{"level":"info","message":"🛡️ Setting up rate limiting...","timestamp":"2025-06-20 19:17:35:1735"}
{"level":"info","message":"📋 Setting up API routes...","timestamp":"2025-06-20 19:17:35:1735"}
{"level":"info","message":"🔗 All routes mounted successfully","timestamp":"2025-06-20 19:17:35:1735"}
{"level":"info","message":"✅ API routes configured","timestamp":"2025-06-20 19:17:35:1735"}
{"level":"info","message":"🚀 Starting server initialization...","timestamp":"2025-06-20 19:18:49:1849"}
{"level":"info","message":"🚀 Starting LajoSpaces Backend Server...","timestamp":"2025-06-20 19:18:49:1849"}
{"level":"info","message":"📊 Connecting to MongoDB...","timestamp":"2025-06-20 19:18:49:1849"}
{"level":"info","message":"📊 Connecting to MongoDB...","timestamp":"2025-06-20 19:18:49:1849"}
{"level":"info","message":"📊 Mongoose connected to MongoDB","timestamp":"2025-06-20 19:18:53:1853"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-20 19:18:53:1853"}
{"level":"info","message":"📊 Connected to database: lajospaces","timestamp":"2025-06-20 19:18:53:1853"}
{"level":"info","message":"✅ MongoDB connection completed","timestamp":"2025-06-20 19:18:53:1853"}
{"level":"info","message":"🛡️ Setting up rate limiting...","timestamp":"2025-06-20 19:18:53:1853"}
{"level":"info","message":"📋 Setting up API routes...","timestamp":"2025-06-20 19:18:53:1853"}
{"level":"info","message":"🔗 All routes mounted successfully","timestamp":"2025-06-20 19:18:53:1853"}
{"level":"info","message":"✅ API routes configured","timestamp":"2025-06-20 19:18:53:1853"}
{"level":"info","message":"🚀 Starting server initialization...","timestamp":"2025-06-20 19:20:08:208"}
{"level":"info","message":"🚀 Starting LajoSpaces Backend Server...","timestamp":"2025-06-20 19:20:08:208"}
{"level":"info","message":"🛡️ Setting up rate limiting...","timestamp":"2025-06-20 19:20:08:208"}
{"level":"info","message":"📋 Setting up API routes...","timestamp":"2025-06-20 19:20:08:208"}
{"level":"info","message":"🔗 All routes mounted successfully","timestamp":"2025-06-20 19:20:08:208"}
{"level":"info","message":"✅ API routes configured","timestamp":"2025-06-20 19:20:08:208"}
{"level":"info","message":"🚀 Starting server initialization...","timestamp":"2025-06-20 19:21:25:2125"}
{"level":"info","message":"🚀 Starting LajoSpaces Backend Server...","timestamp":"2025-06-20 19:21:25:2125"}
{"level":"info","message":"🛡️ Setting up rate limiting...","timestamp":"2025-06-20 19:21:25:2125"}
{"level":"info","message":"📋 Setting up API routes...","timestamp":"2025-06-20 19:21:25:2125"}
{"level":"info","message":"🔗 All routes mounted successfully","timestamp":"2025-06-20 19:21:25:2125"}
{"level":"info","message":"✅ API routes configured","timestamp":"2025-06-20 19:21:25:2125"}
{"level":"info","message":"::1 - - [20/Jun/2025:18:23:14 +0000] \"GET /health HTTP/1.1\" 200 221 \"-\" \"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202\"","timestamp":"2025-06-20 19:23:14:2314"}
{"level":"info","message":"::1 - - [20/Jun/2025:18:23:25 +0000] \"GET /api HTTP/1.1\" 200 502 \"-\" \"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202\"","timestamp":"2025-06-20 19:23:25:2325"}
{"level":"info","message":"🚀 Starting server initialization...","timestamp":"2025-06-20 19:24:07:247"}
{"level":"info","message":"🚀 Starting LajoSpaces Backend Server...","timestamp":"2025-06-20 19:24:07:247"}
{"level":"info","message":"🛡️ Setting up rate limiting...","timestamp":"2025-06-20 19:24:07:247"}
{"level":"info","message":"📋 Setting up API routes...","timestamp":"2025-06-20 19:24:07:247"}
{"level":"info","message":"📧 Email service initialized (verification disabled for testing)","timestamp":"2025-06-20 19:24:07:247"}
{"level":"info","message":"🔗 Mounting auth routes...","timestamp":"2025-06-20 19:24:07:247"}
{"level":"info","message":"🔗 All routes mounted successfully","timestamp":"2025-06-20 19:24:07:247"}
{"level":"info","message":"✅ API routes configured","timestamp":"2025-06-20 19:24:07:247"}
{"error":{"message":"Route /api/auth/health not found on this server","name":"Error","stack":"Error: Route /api/auth/health not found on this server\n    at notFoundHandler (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\middleware\\notFoundHandler.ts:9:17)\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\src\\server.ts:188:3\n    at newFn (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\lajospaces\\lajospacesbackend\\node_modules\\express\\lib\\router\\layer.js:95:5)"},"level":"error","message":"Error 404: Route /api/auth/health not found on this server","request":{"ip":"::1","method":"GET","url":"/api/auth/health","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"},"timestamp":"2025-06-20 19:24:09:249","user":null}
{"level":"info","message":"::1 - - [20/Jun/2025:18:24:09 +0000] \"GET /api/auth/health HTTP/1.1\" 404 344 \"-\" \"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202\"","timestamp":"2025-06-20 19:24:09:249"}
{"endpoint":"/api/auth/health","eventType":"data_viewed","ipAddress":"::1","level":"info","message":"Audit event logged","success":false,"timestamp":"2025-06-20 19:24:13:2413"}
{"level":"info","message":"🚀 Starting server initialization...","timestamp":"2025-06-20 19:24:56:2456"}
{"level":"info","message":"🚀 Starting LajoSpaces Backend Server...","timestamp":"2025-06-20 19:24:56:2456"}
{"level":"info","message":"📊 Connecting to MongoDB...","timestamp":"2025-06-20 19:24:56:2456"}
{"level":"info","message":"📊 Connecting to MongoDB...","timestamp":"2025-06-20 19:24:56:2456"}
